from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QTableWidget, QTableWidgetItem,
                            QHeaderView, QTabWidget, QDateEdit, QComboBox,
                            QGroupBox, QFormLayout, QTextBrowser, QFileDialog,
                            QMessageBox, QFrame, QSizePolicy, QMenu, QAction)
from PyQt5.QtCore import QDate, Qt
from PyQt5.QtGui import QColor, QFont
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
import csv
import json

from database import Sale, Purchase, SaleItem, Inventory, Client, Supplier, Expense, Revenue, Project
from utils import show_error_message, qdate_to_datetime, format_currency

class ReportsWidget(QWidget):
    """واجهة التقارير والتحليل المالي"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.init_ui()

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مطابق تماماً لباقي البرنامج
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(8)

        # إضافة العنوان الرئيسي مطابق تماماً لباقي البرنامج
        title_label = QLabel("📊 إدارة التقارير المتطورة - نظام شامل ومتقدم لإدارة التقارير مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e40af,
                    stop:0.2 #3b82f6,
                    stop:0.4 #6366f1,
                    stop:0.6 #8b5cf6,
                    stop:0.8 #a855f7,
                    stop:1 #c084fc);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;

            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء إطار علوي للأزرار مطابق لباقي البرنامج
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)

        # تخطيط أفقي للأزرار مطابق لباقي البرنامج
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)
        actions_layout.setSpacing(4)

        # إنشاء حاوي عمودي للتوسيط
        top_container = QVBoxLayout()
        top_container.setContentsMargins(6, 0, 6, 0)
        top_container.setSpacing(0)
        top_container.addStretch(1)
        top_container.addLayout(actions_layout)
        top_container.addStretch(1)

        # إنشاء الأزرار مطابقة لباقي البرنامج
        self.sales_report_button = QPushButton("📊 تقرير المبيعات")
        self.style_advanced_button(self.sales_report_button, 'emerald')
        self.sales_report_button.clicked.connect(lambda: self.tabs.setCurrentIndex(0))
        self.sales_report_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.purchases_report_button = QPushButton("🛒 تقرير المشتريات")
        self.style_advanced_button(self.purchases_report_button, 'primary')
        self.purchases_report_button.clicked.connect(lambda: self.tabs.setCurrentIndex(1))
        self.purchases_report_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.profit_loss_button = QPushButton("💰 الأرباح والخسائر")
        self.style_advanced_button(self.profit_loss_button, 'warning')
        self.profit_loss_button.clicked.connect(lambda: self.tabs.setCurrentIndex(2))
        self.profit_loss_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.inventory_report_button = QPushButton("📦 تقرير المخزون")
        self.style_advanced_button(self.inventory_report_button, 'info')
        self.inventory_report_button.clicked.connect(lambda: self.tabs.setCurrentIndex(3))
        self.inventory_report_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.clients_report_button = QPushButton("🤝 تقرير العملاء")
        self.style_advanced_button(self.clients_report_button, 'rose')
        self.clients_report_button.clicked.connect(lambda: self.tabs.setCurrentIndex(4))
        self.clients_report_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.export_button, 'indigo', has_menu=True)
        self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة منسدلة للتصدير
        export_menu = QMenu(self)
        export_menu.setStyleSheet("QMenu { background-color: white; border: 1px solid #E2E8F0; }")

        excel_action = QAction("📊 تصدير إلى Excel", self)
        excel_action.triggered.connect(self.export_to_excel)
        export_menu.addAction(excel_action)

        pdf_action = QAction("📄 تصدير إلى PDF", self)
        pdf_action.triggered.connect(self.export_to_pdf)
        export_menu.addAction(pdf_action)

        csv_action = QAction("📋 تصدير إلى CSV", self)
        csv_action.triggered.connect(self.export_to_csv)
        export_menu.addAction(csv_action)

        self.export_button.setMenu(export_menu)

        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_button, 'modern_teal')
        self.refresh_button.clicked.connect(self.refresh_all_reports)
        self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إضافة الأزرار للتخطيط
        actions_layout.addWidget(self.sales_report_button, 0, Qt.AlignVCenter)
        actions_layout.addWidget(self.purchases_report_button, 0, Qt.AlignVCenter)
        actions_layout.addWidget(self.profit_loss_button, 0, Qt.AlignVCenter)
        actions_layout.addWidget(self.inventory_report_button, 0, Qt.AlignVCenter)
        actions_layout.addWidget(self.clients_report_button, 0, Qt.AlignVCenter)
        actions_layout.addWidget(self.export_button, 0, Qt.AlignVCenter)
        actions_layout.addWidget(self.refresh_button, 0, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي
        top_frame.setLayout(top_container)
        main_layout.addWidget(top_frame)

        # إنشاء تبويبات للتقارير المختلفة مع تصميم محسن ومطابق لباقي البرنامج
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 3px solid #000000;
                border-radius: 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                margin-top: 2px;
            }
            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f1f5f9,
                    stop:0.5 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-bottom: none;
                border-radius: 10px 10px 0 0;
                padding: 12px 24px;
                margin: 1px 2px 0 2px;
                font-weight: bold;
                font-size: 14px;
                color: #1f2937;
                min-width: 150px;
                max-width: 200px;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3b82f6,
                    stop:0.5 #2563eb,
                    stop:1 #1d4ed8);
                color: #ffffff;
                border: 3px solid #1d4ed8;
                border-bottom: none;
                margin-top: 0px;
            }
            QTabBar::tab:hover:!selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #dbeafe,
                    stop:0.5 #bfdbfe,
                    stop:1 #93c5fd);
                color: #1e40af;
                border: 3px solid #3b82f6;
            }
        """)

        # تبويب تقرير المبيعات
        self.sales_tab = QWidget()
        self.setup_sales_tab()
        self.tabs.addTab(self.sales_tab, "📊 تقرير المبيعات")

        # تبويب تقرير المشتريات
        self.purchases_tab = QWidget()
        self.setup_purchases_tab()
        self.tabs.addTab(self.purchases_tab, "🛒 تقرير المشتريات")

        # تبويب تقرير الأرباح والخسائر
        self.profit_loss_tab = QWidget()
        self.setup_profit_loss_tab()
        self.tabs.addTab(self.profit_loss_tab, "💰 الأرباح والخسائر")

        # تبويب تقرير المخزون
        self.inventory_tab = QWidget()
        self.setup_inventory_tab()
        self.tabs.addTab(self.inventory_tab, "📦 تقرير المخزون")

        # تبويب تقرير العملاء
        self.clients_tab = QWidget()
        self.setup_clients_tab()
        self.tabs.addTab(self.clients_tab, "🤝 تقرير العملاء")

        # تبويب تقرير الموردين
        self.suppliers_tab = QWidget()
        self.setup_suppliers_tab()
        self.tabs.addTab(self.suppliers_tab, "🏭 تقرير الموردين")

        # تبويب تقرير المصروفات
        self.expenses_tab = QWidget()
        self.setup_expenses_tab()
        self.tabs.addTab(self.expenses_tab, "💸 تقرير المصروفات")

        # تبويب تقرير الإيرادات
        self.revenues_tab = QWidget()
        self.setup_revenues_tab()
        self.tabs.addTab(self.revenues_tab, "💰 تقرير الإيرادات")

        # تبويب تقرير المشاريع
        self.projects_tab = QWidget()
        self.setup_projects_tab()
        self.tabs.addTab(self.projects_tab, "🏗️ تقرير المشاريع")

        main_layout.addWidget(self.tabs, 1)  # إعطاء التبويبات أولوية في التمدد
        self.setLayout(main_layout)

    def setup_sales_tab(self):
        """إعداد تبويب تقرير المبيعات"""
        layout = QVBoxLayout()

        # مجموعة فلاتر التقرير
        filter_group = QGroupBox("فلاتر التقرير")
        filter_layout = QFormLayout()

        # فلتر الفترة الزمنية
        self.sales_period_combo = QComboBox()
        self.sales_period_combo.addItems(["اليوم", "الأسبوع الحالي", "الشهر الحالي", "الربع الحالي", "السنة الحالية", "فترة محددة"])
        self.sales_period_combo.currentIndexChanged.connect(self.on_sales_period_changed)
        filter_layout.addRow("الفترة الزمنية:", self.sales_period_combo)

        # حقول تاريخ البداية والنهاية
        date_layout = QHBoxLayout()

        self.sales_start_date = QDateEdit()
        self.sales_start_date.setCalendarPopup(True)
        self.sales_start_date.setDate(QDate.currentDate().addMonths(-1))
        self.sales_start_date.setEnabled(False)

        self.sales_end_date = QDateEdit()
        self.sales_end_date.setCalendarPopup(True)
        self.sales_end_date.setDate(QDate.currentDate())
        self.sales_end_date.setEnabled(False)

        date_layout.addWidget(QLabel("من:"))
        date_layout.addWidget(self.sales_start_date)
        date_layout.addWidget(QLabel("إلى:"))
        date_layout.addWidget(self.sales_end_date)

        filter_layout.addRow("", date_layout)

        # فلتر العميل
        self.sales_client_combo = QComboBox()
        self.sales_client_combo.addItem("جميع العملاء", None)
        clients = self.session.query(Client).order_by(Client.name).all()
        for client in clients:
            self.sales_client_combo.addItem(client.name, client.id)
        filter_layout.addRow("العميل:", self.sales_client_combo)

        # فلتر الحالة
        self.sales_status_combo = QComboBox()
        self.sales_status_combo.addItem("جميع الحالات", None)
        self.sales_status_combo.addItem("في الانتظار", "pending")
        self.sales_status_combo.addItem("مكتملة", "completed")
        self.sales_status_combo.addItem("ملغية", "cancelled")
        self.sales_status_combo.addItem("مرتجعة", "returned")
        filter_layout.addRow("الحالة:", self.sales_status_combo)

        # زر تطبيق الفلاتر مطابق لباقي البرنامج
        self.sales_apply_button = QPushButton("📊 تطبيق التقرير")
        self.style_advanced_button(self.sales_apply_button, 'emerald')
        self.sales_apply_button.clicked.connect(self.generate_sales_report)
        filter_layout.addRow("", self.sales_apply_button)

        filter_group.setLayout(filter_layout)

        # مجموعة نتائج التقرير
        results_group = QGroupBox("نتائج التقرير")
        results_layout = QVBoxLayout()

        # ملخص المبيعات
        summary_layout = QHBoxLayout()

        self.sales_count_label = QLabel("عدد الفواتير: 0")

        self.sales_total_label = QLabel("إجمالي المبيعات: 0.00")

        self.sales_profit_label = QLabel("إجمالي الأرباح: 0.00")

        summary_layout.addWidget(self.sales_count_label)
        summary_layout.addWidget(self.sales_total_label)
        summary_layout.addWidget(self.sales_profit_label)

        # جدول المبيعات مطابق لباقي البرنامج
        self.sales_table = QTableWidget()
        self.sales_table.setColumnCount(7)
        self.sales_table.setHorizontalHeaderLabels(["رقم الفاتورة", "العميل", "التاريخ", "المبلغ الإجمالي", "المبلغ المدفوع", "طريقة الدفع", "الحالة"])

        # تطبيق التصميم الموحد للجدول
        self.sales_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.3);
                background-color: #ffffff;
                alternate-background-color: #f8f9fa;
                selection-background-color: rgba(66, 133, 244, 0.2);
                selection-color: #1a73e8;
                border: 3px solid #000000;
                border-radius: 12px;
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-size: 12px;
                color: #2c3e50;
            }
            QTableWidget::item {
                padding: 12px 8px;
                border-bottom: 1px solid rgba(0, 0, 0, 0.1);
                border-right: 1px solid rgba(0, 0, 0, 0.05);
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea,
                    stop:0.5 #764ba2,
                    stop:1 #667eea);
                color: #ffffff;
                padding: 12px 8px;
                border: 1px solid #5a67d8;
                font-weight: bold;
                font-size: 13px;
                text-align: center;
            }
        """)

        self.sales_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.sales_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.sales_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.sales_table.setAlternatingRowColors(True)

        results_layout.addLayout(summary_layout)
        results_layout.addWidget(self.sales_table)

        results_group.setLayout(results_layout)

        layout.addWidget(filter_group)
        layout.addWidget(results_group)

        self.sales_tab.setLayout(layout)

    def setup_purchases_tab(self):
        """إعداد تبويب تقرير المشتريات"""
        layout = QVBoxLayout()

        # مجموعة فلاتر التقرير
        filter_group = QGroupBox("فلاتر التقرير")
        filter_group
        filter_layout = QFormLayout()

        # فلتر الفترة الزمنية
        self.purchases_period_combo = QComboBox()
        self.purchases_period_combo.addItems(["اليوم", "الأسبوع الحالي", "الشهر الحالي", "الربع الحالي", "السنة الحالية", "فترة محددة"])
        self.purchases_period_combo.currentIndexChanged.connect(self.on_purchases_period_changed)
        filter_layout.addRow("الفترة الزمنية:", self.purchases_period_combo)

        # حقول تاريخ البداية والنهاية
        date_layout = QHBoxLayout()

        self.purchases_start_date = QDateEdit()
        self.purchases_start_date.setCalendarPopup(True)
        self.purchases_start_date.setDate(QDate.currentDate().addMonths(-1))
        self.purchases_start_date.setEnabled(False)

        self.purchases_end_date = QDateEdit()
        self.purchases_end_date.setCalendarPopup(True)
        self.purchases_end_date.setDate(QDate.currentDate())
        self.purchases_end_date.setEnabled(False)

        date_layout.addWidget(QLabel("من:"))
        date_layout.addWidget(self.purchases_start_date)
        date_layout.addWidget(QLabel("إلى:"))
        date_layout.addWidget(self.purchases_end_date)

        filter_layout.addRow("", date_layout)

        # فلتر المورد
        self.purchases_supplier_combo = QComboBox()
        self.purchases_supplier_combo.addItem("جميع الموردين", None)
        suppliers = self.session.query(Supplier).order_by(Supplier.name).all()
        for supplier in suppliers:
            self.purchases_supplier_combo.addItem(supplier.name, supplier.id)
        filter_layout.addRow("المورد:", self.purchases_supplier_combo)

        # فلتر الحالة
        self.purchases_status_combo = QComboBox()
        self.purchases_status_combo.addItem("جميع الحالات", None)
        self.purchases_status_combo.addItem("في الانتظار", "pending")
        self.purchases_status_combo.addItem("مستلم جزئياً", "partially_received")
        self.purchases_status_combo.addItem("مستلم بالكامل", "completed")
        self.purchases_status_combo.addItem("ملغي", "cancelled")
        filter_layout.addRow("الحالة:", self.purchases_status_combo)

        # زر تطبيق الفلاتر مطابق لباقي البرنامج
        self.purchases_apply_button = QPushButton("🛒 تطبيق التقرير")
        self.style_advanced_button(self.purchases_apply_button, 'primary')
        self.purchases_apply_button.clicked.connect(self.generate_purchases_report)
        filter_layout.addRow("", self.purchases_apply_button)

        filter_group.setLayout(filter_layout)

        # مجموعة نتائج التقرير
        results_group = QGroupBox("نتائج التقرير")
        results_group
        results_layout = QVBoxLayout()

        # ملخص المشتريات
        summary_layout = QHBoxLayout()

        self.purchases_count_label = QLabel("عدد أوامر الشراء: 0")
        self.purchases_count_label

        self.purchases_total_label = QLabel("إجمالي المشتريات: 0.00")
        self.purchases_total_label

        self.purchases_paid_label = QLabel("إجمالي المدفوعات: 0.00")
        self.purchases_paid_label

        summary_layout.addWidget(self.purchases_count_label)
        summary_layout.addWidget(self.purchases_total_label)
        summary_layout.addWidget(self.purchases_paid_label)

        # جدول المشتريات مطابق لباقي البرنامج
        self.purchases_table = QTableWidget()
        self.purchases_table.setColumnCount(6)
        self.purchases_table.setHorizontalHeaderLabels(["رقم أمر الشراء", "المورد", "التاريخ", "المبلغ الإجمالي", "المبلغ المدفوع", "الحالة"])

        # تطبيق التصميم الموحد للجدول
        self.purchases_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.3);
                background-color: #ffffff;
                alternate-background-color: #f8f9fa;
                selection-background-color: rgba(66, 133, 244, 0.2);
                selection-color: #1a73e8;
                border: 3px solid #000000;
                border-radius: 12px;
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-size: 12px;
                color: #2c3e50;
            }
            QTableWidget::item {
                padding: 12px 8px;
                border-bottom: 1px solid rgba(0, 0, 0, 0.1);
                border-right: 1px solid rgba(0, 0, 0, 0.05);
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea,
                    stop:0.5 #764ba2,
                    stop:1 #667eea);
                color: #ffffff;
                padding: 12px 8px;
                border: 1px solid #5a67d8;
                font-weight: bold;
                font-size: 13px;
                text-align: center;
            }
        """)

        self.purchases_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.purchases_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.purchases_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.purchases_table.setAlternatingRowColors(True)

        results_layout.addLayout(summary_layout)
        results_layout.addWidget(self.purchases_table)

        results_group.setLayout(results_layout)

        layout.addWidget(filter_group)
        layout.addWidget(results_group)

        self.purchases_tab.setLayout(layout)

    def setup_profit_loss_tab(self):
        """إعداد تبويب تقرير الأرباح والخسائر"""
        layout = QVBoxLayout()

        # مجموعة فلاتر التقرير
        filter_group = QGroupBox("فلاتر التقرير")
        filter_group
        filter_layout = QFormLayout()

        # فلتر الفترة الزمنية
        self.profit_loss_period_combo = QComboBox()
        self.profit_loss_period_combo.addItems(["اليوم", "الأسبوع الحالي", "الشهر الحالي", "الربع الحالي", "السنة الحالية", "فترة محددة"])
        self.profit_loss_period_combo.currentIndexChanged.connect(self.on_profit_loss_period_changed)
        filter_layout.addRow("الفترة الزمنية:", self.profit_loss_period_combo)

        # حقول تاريخ البداية والنهاية
        date_layout = QHBoxLayout()

        self.profit_loss_start_date = QDateEdit()
        self.profit_loss_start_date.setCalendarPopup(True)
        self.profit_loss_start_date.setDate(QDate.currentDate().addMonths(-1))
        self.profit_loss_start_date.setEnabled(False)

        self.profit_loss_end_date = QDateEdit()
        self.profit_loss_end_date.setCalendarPopup(True)
        self.profit_loss_end_date.setDate(QDate.currentDate())
        self.profit_loss_end_date.setEnabled(False)

        date_layout.addWidget(QLabel("من:"))
        date_layout.addWidget(self.profit_loss_start_date)
        date_layout.addWidget(QLabel("إلى:"))
        date_layout.addWidget(self.profit_loss_end_date)

        filter_layout.addRow("", date_layout)

        # زر تطبيق الفلاتر مطابق لباقي البرنامج
        self.profit_loss_apply_button = QPushButton("💰 تطبيق التقرير")
        self.style_advanced_button(self.profit_loss_apply_button, 'warning')
        self.profit_loss_apply_button.clicked.connect(self.generate_profit_loss_report)
        filter_layout.addRow("", self.profit_loss_apply_button)

        filter_group.setLayout(filter_layout)

        # مجموعة نتائج التقرير
        results_group = QGroupBox("نتائج التقرير")
        results_group
        results_layout = QVBoxLayout()

        # تقرير الأرباح والخسائر
        self.profit_loss_summary = QTextBrowser()
        self.profit_loss_summary.setMinimumHeight(400)
        self.profit_loss_summary

        results_layout.addWidget(self.profit_loss_summary)

        results_group.setLayout(results_layout)

        layout.addWidget(filter_group)
        layout.addWidget(results_group)

        self.profit_loss_tab.setLayout(layout)

    def setup_inventory_tab(self):
        """إعداد تبويب تقرير المخزون"""
        layout = QVBoxLayout()

        # مجموعة فلاتر التقرير
        filter_group = QGroupBox("فلاتر التقرير")
        filter_group
        filter_layout = QFormLayout()

        # فلتر الفئة
        self.inventory_category_combo = QComboBox()
        self.inventory_category_combo.addItem("جميع الفئات", None)
        categories = self.session.query(Inventory.category).distinct().all()
        for category in categories:
            if category[0]:
                self.inventory_category_combo.addItem(category[0], category[0])
        filter_layout.addRow("الفئة:", self.inventory_category_combo)

        # فلتر المورد
        self.inventory_supplier_combo = QComboBox()
        self.inventory_supplier_combo.addItem("جميع الموردين", None)
        suppliers = self.session.query(Supplier).order_by(Supplier.name).all()
        for supplier in suppliers:
            self.inventory_supplier_combo.addItem(supplier.name, supplier.id)
        filter_layout.addRow("المورد:", self.inventory_supplier_combo)

        # فلتر حالة المخزون
        self.inventory_status_combo = QComboBox()
        self.inventory_status_combo.addItem("جميع العناصر", "all")
        self.inventory_status_combo.addItem("عناصر منخفضة المخزون", "low_stock")
        self.inventory_status_combo.addItem("عناصر نفدت من المخزون", "out_of_stock")
        self.inventory_status_combo.addItem("عناصر متوفرة", "in_stock")
        filter_layout.addRow("حالة المخزون:", self.inventory_status_combo)

        # زر تطبيق الفلاتر مطابق لباقي البرنامج
        self.inventory_apply_button = QPushButton("📦 تطبيق التقرير")
        self.style_advanced_button(self.inventory_apply_button, 'info')
        self.inventory_apply_button.clicked.connect(self.generate_inventory_report)
        filter_layout.addRow("", self.inventory_apply_button)

        filter_group.setLayout(filter_layout)

        # مجموعة نتائج التقرير
        results_group = QGroupBox("نتائج التقرير")
        results_group
        results_layout = QVBoxLayout()

        # ملخص المخزون
        summary_layout = QHBoxLayout()

        self.inventory_count_label = QLabel("عدد العناصر: 0")
        self.inventory_count_label

        self.inventory_value_label = QLabel("قيمة المخزون: 0.00")
        self.inventory_value_label

        self.inventory_low_stock_label = QLabel("عناصر منخفضة المخزون: 0")
        self.inventory_low_stock_label

        summary_layout.addWidget(self.inventory_count_label)
        summary_layout.addWidget(self.inventory_value_label)
        summary_layout.addWidget(self.inventory_low_stock_label)

        # جدول المخزون مطابق لباقي البرنامج
        self.inventory_table = QTableWidget()
        self.inventory_table.setColumnCount(8)
        self.inventory_table.setHorizontalHeaderLabels(["الاسم", "الفئة", "الوحدة", "الكمية", "الحد الأدنى", "سعر التكلفة", "سعر البيع", "المورد"])

        # تطبيق التصميم الموحد للجدول
        self.inventory_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.3);
                background-color: #ffffff;
                alternate-background-color: #f8f9fa;
                selection-background-color: rgba(66, 133, 244, 0.2);
                selection-color: #1a73e8;
                border: 3px solid #000000;
                border-radius: 12px;
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-size: 12px;
                color: #2c3e50;
            }
            QTableWidget::item {
                padding: 12px 8px;
                border-bottom: 1px solid rgba(0, 0, 0, 0.1);
                border-right: 1px solid rgba(0, 0, 0, 0.05);
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea,
                    stop:0.5 #764ba2,
                    stop:1 #667eea);
                color: #ffffff;
                padding: 12px 8px;
                border: 1px solid #5a67d8;
                font-weight: bold;
                font-size: 13px;
                text-align: center;
            }
        """)

        self.inventory_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.inventory_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.inventory_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.inventory_table.setAlternatingRowColors(True)

        results_layout.addLayout(summary_layout)
        results_layout.addWidget(self.inventory_table)

        results_group.setLayout(results_layout)

        layout.addWidget(filter_group)
        layout.addWidget(results_group)

        self.inventory_tab.setLayout(layout)

    def setup_clients_tab(self):
        """إعداد تبويب تقرير العملاء"""
        layout = QVBoxLayout()

        # مجموعة فلاتر التقرير
        filter_group = QGroupBox("فلاتر التقرير")
        filter_group
        filter_layout = QFormLayout()

        # فلتر العميل
        self.clients_client_combo = QComboBox()
        self.clients_client_combo.addItem("جميع العملاء", None)
        clients = self.session.query(Client).order_by(Client.name).all()
        for client in clients:
            self.clients_client_combo.addItem(client.name, client.id)
        filter_layout.addRow("العميل:", self.clients_client_combo)

        # زر تطبيق الفلاتر مطابق لباقي البرنامج
        self.clients_apply_button = QPushButton("👥 تطبيق التقرير")
        self.style_advanced_button(self.clients_apply_button, 'rose')
        self.clients_apply_button.clicked.connect(self.generate_clients_report)
        filter_layout.addRow("", self.clients_apply_button)

        filter_group.setLayout(filter_layout)

        # مجموعة نتائج التقرير
        results_group = QGroupBox("نتائج التقرير")
        results_group
        results_layout = QVBoxLayout()

        # تقرير العملاء
        self.clients_summary = QTextBrowser()
        self.clients_summary.setMinimumHeight(200)
        self.clients_summary

        # جدول مبيعات العميل مطابق لباقي البرنامج
        self.clients_sales_table = QTableWidget()
        self.clients_sales_table.setColumnCount(6)
        self.clients_sales_table.setHorizontalHeaderLabels(["رقم الفاتورة", "التاريخ", "المبلغ الإجمالي", "المبلغ المدفوع", "طريقة الدفع", "الحالة"])

        # تطبيق التصميم الموحد للجدول
        self.clients_sales_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.3);
                background-color: #ffffff;
                alternate-background-color: #f8f9fa;
                selection-background-color: rgba(66, 133, 244, 0.2);
                selection-color: #1a73e8;
                border: 3px solid #000000;
                border-radius: 12px;
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-size: 12px;
                color: #2c3e50;
            }
            QTableWidget::item {
                padding: 12px 8px;
                border-bottom: 1px solid rgba(0, 0, 0, 0.1);
                border-right: 1px solid rgba(0, 0, 0, 0.05);
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea,
                    stop:0.5 #764ba2,
                    stop:1 #667eea);
                color: #ffffff;
                padding: 12px 8px;
                border: 1px solid #5a67d8;
                font-weight: bold;
                font-size: 13px;
                text-align: center;
            }
        """)

        self.clients_sales_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.clients_sales_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.clients_sales_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.clients_sales_table.setAlternatingRowColors(True)

        results_layout.addWidget(self.clients_summary)
        results_layout.addWidget(QLabel("مبيعات العميل:"))
        results_layout.addWidget(self.clients_sales_table)

        results_group.setLayout(results_layout)

        layout.addWidget(filter_group)
        layout.addWidget(results_group)

        self.clients_tab.setLayout(layout)

    # دوال معالجة الأحداث
    def on_sales_period_changed(self, index):
        """معالجة تغيير الفترة الزمنية في تقرير المبيعات"""
        try:
            period = self.sales_period_combo.currentText()
            if period == "فترة محددة":
                self.sales_start_date.setEnabled(True)
                self.sales_end_date.setEnabled(True)
            else:
                self.sales_start_date.setEnabled(False)
                self.sales_end_date.setEnabled(False)
                self.set_date_range(period, self.sales_start_date, self.sales_end_date)
        except Exception as e:
            print(f"خطأ في معالجة تغيير الفترة الزمنية في تقرير المبيعات: {str(e)}")

    def on_purchases_period_changed(self, index):
        """معالجة تغيير الفترة الزمنية في تقرير المشتريات"""
        try:
            period = self.purchases_period_combo.currentText()
            if period == "فترة محددة":
                self.purchases_start_date.setEnabled(True)
                self.purchases_end_date.setEnabled(True)
            else:
                self.purchases_start_date.setEnabled(False)
                self.purchases_end_date.setEnabled(False)
                self.set_date_range(period, self.purchases_start_date, self.purchases_end_date)
        except Exception as e:
            print(f"خطأ في معالجة تغيير الفترة الزمنية في تقرير المشتريات: {str(e)}")

    def on_profit_loss_period_changed(self, index):
        """معالجة تغيير الفترة الزمنية في تقرير الأرباح والخسائر"""
        try:
            period = self.profit_loss_period_combo.currentText()
            if period == "فترة محددة":
                self.profit_loss_start_date.setEnabled(True)
                self.profit_loss_end_date.setEnabled(True)
            else:
                self.profit_loss_start_date.setEnabled(False)
                self.profit_loss_end_date.setEnabled(False)
                self.set_date_range(period, self.profit_loss_start_date, self.profit_loss_end_date)
        except Exception as e:
            print(f"خطأ في معالجة تغيير الفترة الزمنية في تقرير الأرباح والخسائر: {str(e)}")

    def on_expenses_period_changed(self, index):
        """معالجة تغيير الفترة الزمنية في تقرير المصروفات"""
        try:
            period = self.expenses_period_combo.currentText()
            if period == "فترة محددة":
                self.expenses_start_date.setEnabled(True)
                self.expenses_end_date.setEnabled(True)
            else:
                self.expenses_start_date.setEnabled(False)
                self.expenses_end_date.setEnabled(False)
                self.set_date_range(period, self.expenses_start_date, self.expenses_end_date)
        except Exception as e:
            print(f"خطأ في معالجة تغيير الفترة الزمنية في تقرير المصروفات: {str(e)}")

    def on_revenues_period_changed(self, index):
        """معالجة تغيير الفترة الزمنية في تقرير الإيرادات"""
        try:
            period = self.revenues_period_combo.currentText()
            if period == "فترة محددة":
                self.revenues_start_date.setEnabled(True)
                self.revenues_end_date.setEnabled(True)
            else:
                self.revenues_start_date.setEnabled(False)
                self.revenues_end_date.setEnabled(False)
                self.set_date_range(period, self.revenues_start_date, self.revenues_end_date)
        except Exception as e:
            print(f"خطأ في معالجة تغيير الفترة الزمنية في تقرير الإيرادات: {str(e)}")

    def set_date_range(self, period, start_date_widget, end_date_widget):
        """تعيين نطاق التاريخ بناءً على الفترة المحددة"""
        today = QDate.currentDate()

        if period == "اليوم":
            start_date_widget.setDate(today)
            end_date_widget.setDate(today)
        elif period == "الأسبوع الحالي":
            start_of_week = today.addDays(-today.dayOfWeek() + 1)
            start_date_widget.setDate(start_of_week)
            end_date_widget.setDate(today)
        elif period == "الشهر الحالي":
            start_of_month = QDate(today.year(), today.month(), 1)
            start_date_widget.setDate(start_of_month)
            end_date_widget.setDate(today)
        elif period == "الربع الحالي":
            quarter = (today.month() - 1) // 3 + 1
            start_month = (quarter - 1) * 3 + 1
            start_of_quarter = QDate(today.year(), start_month, 1)
            start_date_widget.setDate(start_of_quarter)
            end_date_widget.setDate(today)
        elif period == "السنة الحالية":
            start_of_year = QDate(today.year(), 1, 1)
            start_date_widget.setDate(start_of_year)
            end_date_widget.setDate(today)

    # دوال إنتاج التقارير
    def generate_sales_report(self):
        """إنتاج تقرير المبيعات"""
        try:
            # الحصول على الفلاتر
            start_date = qdate_to_datetime(self.sales_start_date.date())
            end_date = qdate_to_datetime(self.sales_end_date.date())
            client_id = self.sales_client_combo.currentData()
            status = self.sales_status_combo.currentData()

            # بناء الاستعلام
            query = self.session.query(Sale).filter(
                Sale.date >= start_date,
                Sale.date <= end_date
            )

            # تطبيق الفلاتر
            if client_id:
                query = query.filter(Sale.client_id == client_id)
            if status:
                query = query.filter(Sale.status == status)

            # تنفيذ الاستعلام
            sales = query.order_by(Sale.date.desc()).all()

            # حساب الإحصائيات
            total_sales = sum(sale.total_amount for sale in sales)
            total_profit = 0

            # حساب الأرباح من عناصر المبيعات
            for sale in sales:
                sale_items = self.session.query(SaleItem).filter(SaleItem.sale_id == sale.id).all()
                for item in sale_items:
                    inventory_item = self.session.query(Inventory).get(item.inventory_id)
                    if inventory_item:
                        profit_per_unit = item.unit_price - inventory_item.cost_price
                        total_profit += profit_per_unit * item.quantity

            # تحديث الملخص
            self.sales_count_label.label.setText(f"عدد الفواتير: {len(sales)}")
            self.sales_total_label.label.setText(f"إجمالي المبيعات: {format_currency(total_sales)}")
            self.sales_profit_label.label.setText(f"إجمالي الأرباح: {format_currency(total_profit)}")

            # تحديث الجدول
            self.sales_table.setRowCount(len(sales))
            for row, sale in enumerate(sales):
                # رقم الفاتورة
                self.sales_table.setItem(row, 0, QTableWidgetItem(sale.sale_number or ""))

                # العميل
                client_name = ""
                if sale.client:
                    client_name = sale.client.name
                self.sales_table.setItem(row, 1, QTableWidgetItem(client_name))

                # التاريخ
                date_str = sale.date.strftime("%Y-%m-%d") if sale.date else ""
                self.sales_table.setItem(row, 2, QTableWidgetItem(date_str))

                # المبلغ الإجمالي
                self.sales_table.setItem(row, 3, QTableWidgetItem(format_currency(sale.total_amount)))

                # المبلغ المدفوع
                self.sales_table.setItem(row, 4, QTableWidgetItem(format_currency(sale.paid_amount)))

                # طريقة الدفع
                payment_methods = {
                    'cash': 'نقدي',
                    'credit': 'آجل',
                    'bank_transfer': 'تحويل بنكي',
                    'check': 'شيك'
                }
                payment_method = payment_methods.get(sale.payment_method, sale.payment_method or "")
                self.sales_table.setItem(row, 5, QTableWidgetItem(payment_method))

                # الحالة
                statuses = {
                    'pending': 'في الانتظار',
                    'completed': 'مكتملة',
                    'cancelled': 'ملغية',
                    'returned': 'مرتجعة'
                }
                status_text = statuses.get(sale.status, sale.status or "")
                self.sales_table.setItem(row, 6, QTableWidgetItem(status_text))

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنتاج تقرير المبيعات: {str(e)}")

    def generate_purchases_report(self):
        """إنتاج تقرير المشتريات"""
        try:
            # الحصول على الفلاتر
            start_date = qdate_to_datetime(self.purchases_start_date.date())
            end_date = qdate_to_datetime(self.purchases_end_date.date())
            supplier_id = self.purchases_supplier_combo.currentData()
            status = self.purchases_status_combo.currentData()

            # بناء الاستعلام
            query = self.session.query(Purchase).filter(
                Purchase.date >= start_date,
                Purchase.date <= end_date
            )

            # تطبيق الفلاتر
            if supplier_id:
                query = query.filter(Purchase.supplier_id == supplier_id)
            if status:
                query = query.filter(Purchase.status == status)

            # تنفيذ الاستعلام
            purchases = query.order_by(Purchase.date.desc()).all()

            # حساب الإحصائيات
            total_purchases = sum(purchase.total_amount for purchase in purchases)
            total_paid = sum(purchase.paid_amount for purchase in purchases)

            # تحديث الملخص
            self.purchases_count_label.label.setText(f"عدد أوامر الشراء: {len(purchases)}")
            self.purchases_total_label.label.setText(f"إجمالي المشتريات: {format_currency(total_purchases)}")
            self.purchases_paid_label.label.setText(f"إجمالي المدفوعات: {format_currency(total_paid)}")

            # تحديث الجدول
            self.purchases_table.setRowCount(len(purchases))
            for row, purchase in enumerate(purchases):
                # رقم أمر الشراء
                self.purchases_table.setItem(row, 0, QTableWidgetItem(purchase.purchase_number or ""))

                # المورد
                supplier_name = ""
                if purchase.supplier:
                    supplier_name = purchase.supplier.name
                self.purchases_table.setItem(row, 1, QTableWidgetItem(supplier_name))

                # التاريخ
                date_str = purchase.date.strftime("%Y-%m-%d") if purchase.date else ""
                self.purchases_table.setItem(row, 2, QTableWidgetItem(date_str))

                # المبلغ الإجمالي
                self.purchases_table.setItem(row, 3, QTableWidgetItem(format_currency(purchase.total_amount)))

                # المبلغ المدفوع
                self.purchases_table.setItem(row, 4, QTableWidgetItem(format_currency(purchase.paid_amount)))

                # الحالة
                statuses = {
                    'pending': 'في الانتظار',
                    'partially_received': 'مستلم جزئياً',
                    'completed': 'مستلم بالكامل',
                    'cancelled': 'ملغي'
                }
                status_text = statuses.get(purchase.status, purchase.status or "")
                self.purchases_table.setItem(row, 5, QTableWidgetItem(status_text))

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنتاج تقرير المشتريات: {str(e)}")

    def generate_profit_loss_report(self):
        """إنتاج تقرير الأرباح والخسائر"""
        try:
            # الحصول على الفلاتر
            start_date = qdate_to_datetime(self.profit_loss_start_date.date())
            end_date = qdate_to_datetime(self.profit_loss_end_date.date())

            # حساب إجمالي المبيعات والأرباح
            sales = self.session.query(Sale).filter(
                Sale.date >= start_date,
                Sale.date <= end_date,
                Sale.status == 'completed'
            ).all()

            total_sales_revenue = sum(sale.total_amount for sale in sales)
            total_sales_cost = 0
            total_sales_profit = 0

            # حساب تكلفة المبيعات والأرباح
            for sale in sales:
                sale_items = self.session.query(SaleItem).filter(SaleItem.sale_id == sale.id).all()
                for item in sale_items:
                    inventory_item = self.session.query(Inventory).get(item.inventory_id)
                    if inventory_item:
                        item_cost = inventory_item.cost_price * item.quantity
                        total_sales_cost += item_cost
                        total_sales_profit += (item.unit_price * item.quantity) - item_cost

            # حساب إجمالي المشتريات
            purchases = self.session.query(Purchase).filter(
                Purchase.date >= start_date,
                Purchase.date <= end_date
            ).all()

            total_purchases = sum(purchase.total_amount for purchase in purchases)

            # حساب صافي الربح/الخسارة
            net_profit_loss = total_sales_profit

            # إنشاء تقرير HTML
            html_report = f"""
            <html>
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; direction: rtl; }}
                    .header {{ text-align: center; color: #2c3e50; margin-bottom: 20px; }}
                    .section {{ margin: 15px 0; padding: 10px; border: 1px solid #bdc3c7; border-radius: 5px; }}
                    .positive {{ color: #27ae60; font-weight: bold; }}
                    .negative {{ color: #e74c3c; font-weight: bold; }}
                    .neutral {{ color: #34495e; font-weight: bold; }}
                    .total {{ font-size: 18px; background-color: #ecf0f1; padding: 10px; border-radius: 5px; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h2>تقرير الأرباح والخسائر</h2>
                    <p>من {start_date.strftime('%Y-%m-%d')} إلى {end_date.strftime('%Y-%m-%d')}</p>
                </div>

                <div class="section">
                    <h3>الإيرادات</h3>
                    <p>إجمالي المبيعات: <span class="positive">{format_currency(total_sales_revenue)}</span></p>
                    <p>عدد فواتير المبيعات: <span class="neutral">{len(sales)}</span></p>
                </div>

                <div class="section">
                    <h3>التكاليف</h3>
                    <p>تكلفة البضاعة المباعة: <span class="negative">{format_currency(total_sales_cost)}</span></p>
                    <p>إجمالي المشتريات: <span class="negative">{format_currency(total_purchases)}</span></p>
                    <p>عدد أوامر الشراء: <span class="neutral">{len(purchases)}</span></p>
                </div>

                <div class="section">
                    <h3>الأرباح</h3>
                    <p>إجمالي ربح المبيعات: <span class="positive">{format_currency(total_sales_profit)}</span></p>
                    <p>هامش الربح: <span class="neutral">{(total_sales_profit / total_sales_revenue * 100) if total_sales_revenue > 0 else 0:.2f}%</span></p>
                </div>

                <div class="section total">
                    <h3>النتيجة النهائية</h3>
                    <p>صافي الربح/الخسارة: <span class="{'positive' if net_profit_loss >= 0 else 'negative'}">{format_currency(net_profit_loss)}</span></p>
                </div>
            </body>
            </html>
            """

            self.profit_loss_summary.setHtml(html_report)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنتاج تقرير الأرباح والخسائر: {str(e)}")

    def generate_inventory_report(self):
        """إنتاج تقرير المخزون"""
        try:
            # الحصول على الفلاتر
            category = self.inventory_category_combo.currentData()
            supplier_id = self.inventory_supplier_combo.currentData()
            status = self.inventory_status_combo.currentData()

            # بناء الاستعلام
            query = self.session.query(Inventory)

            # تطبيق الفلاتر
            if category:
                query = query.filter(Inventory.category == category)
            if supplier_id:
                query = query.filter(Inventory.supplier_id == supplier_id)

            # تطبيق فلتر حالة المخزون
            if status == "low_stock":
                query = query.filter(Inventory.quantity <= Inventory.min_quantity)
            elif status == "out_of_stock":
                query = query.filter(Inventory.quantity == 0)
            elif status == "in_stock":
                query = query.filter(Inventory.quantity > 0)

            # تنفيذ الاستعلام
            inventory_items = query.order_by(Inventory.name).all()

            # حساب الإحصائيات
            total_items = len(inventory_items)
            total_value = sum(item.cost_price * item.quantity for item in inventory_items)
            low_stock_items = len([item for item in inventory_items if item.quantity <= item.min_quantity])

            # تحديث الملخص
            self.inventory_count_label.label.setText(f"عدد العناصر: {total_items}")
            self.inventory_value_label.label.setText(f"قيمة المخزون: {format_currency(total_value)}")
            self.inventory_low_stock_label.label.setText(f"عناصر منخفضة المخزون: {low_stock_items}")

            # تحديث الجدول
            self.inventory_table.setRowCount(len(inventory_items))
            for row, item in enumerate(inventory_items):
                # الاسم
                self.inventory_table.setItem(row, 0, QTableWidgetItem(item.name or ""))

                # الفئة
                self.inventory_table.setItem(row, 1, QTableWidgetItem(item.category or ""))

                # الوحدة
                self.inventory_table.setItem(row, 2, QTableWidgetItem(item.unit or ""))

                # الكمية
                quantity_item = QTableWidgetItem(str(int(item.quantity)))
                if item.quantity <= item.min_quantity:
                    quantity_item.setBackground(QColor(255, 235, 235))  # خلفية حمراء فاتحة
                self.inventory_table.setItem(row, 3, quantity_item)

                # الحد الأدنى
                self.inventory_table.setItem(row, 4, QTableWidgetItem(str(int(item.min_quantity))))

                # سعر التكلفة
                self.inventory_table.setItem(row, 5, QTableWidgetItem(format_currency(item.cost_price)))

                # سعر البيع
                self.inventory_table.setItem(row, 6, QTableWidgetItem(format_currency(item.selling_price)))

                # المورد
                supplier_name = ""
                if item.supplier:
                    supplier_name = item.supplier.name
                self.inventory_table.setItem(row, 7, QTableWidgetItem(supplier_name))

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنتاج تقرير المخزون: {str(e)}")

    def generate_clients_report(self):
        """إنتاج تقرير العملاء"""
        try:
            # الحصول على الفلاتر
            client_id = self.clients_client_combo.currentData()

            if client_id:
                # تقرير عميل محدد
                client = self.session.query(Client).get(client_id)
                if not client:
                    show_error_message("خطأ", "العميل المحدد غير موجود")
                    return

                # الحصول على مبيعات العميل
                sales = self.session.query(Sale).filter(Sale.client_id == client_id).order_by(Sale.date.desc()).all()

                # حساب الإحصائيات
                total_sales = sum(sale.total_amount for sale in sales)
                total_paid = sum(sale.paid_amount for sale in sales)
                remaining_amount = total_sales - total_paid

                # إنشاء تقرير HTML
                html_report = f"""
                <html>
                <head>
                    <style>
                        body {{ font-family: Arial, sans-serif; direction: rtl; }}
                        .header {{ text-align: center; color: #2c3e50; margin-bottom: 20px; }}
                        .info {{ margin: 10px 0; }}
                        .positive {{ color: #27ae60; font-weight: bold; }}
                        .negative {{ color: #e74c3c; font-weight: bold; }}
                        .neutral {{ color: #34495e; font-weight: bold; }}
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h2>تقرير العميل: {client.name}</h2>
                    </div>
                    <div class="info">
                        <p><strong>الهاتف:</strong> {client.phone or 'غير محدد'}</p>
                        <p><strong>العنوان:</strong> {client.address or 'غير محدد'}</p>
                        <p><strong>عدد الفواتير:</strong> <span class="neutral">{len(sales)}</span></p>
                        <p><strong>إجمالي المبيعات:</strong> <span class="positive">{format_currency(total_sales)}</span></p>
                        <p><strong>إجمالي المدفوعات:</strong> <span class="positive">{format_currency(total_paid)}</span></p>
                        <p><strong>المبلغ المتبقي:</strong> <span class="{'negative' if remaining_amount > 0 else 'positive'}">{format_currency(remaining_amount)}</span></p>
                    </div>
                </body>
                </html>
                """

                self.clients_summary.setHtml(html_report)

                # تحديث جدول المبيعات
                self.clients_sales_table.setRowCount(len(sales))
                for row, sale in enumerate(sales):
                    # رقم الفاتورة
                    self.clients_sales_table.setItem(row, 0, QTableWidgetItem(sale.sale_number or ""))

                    # التاريخ
                    date_str = sale.date.strftime("%Y-%m-%d") if sale.date else ""
                    self.clients_sales_table.setItem(row, 1, QTableWidgetItem(date_str))

                    # المبلغ الإجمالي
                    self.clients_sales_table.setItem(row, 2, QTableWidgetItem(format_currency(sale.total_amount)))

                    # المبلغ المدفوع
                    self.clients_sales_table.setItem(row, 3, QTableWidgetItem(format_currency(sale.paid_amount)))

                    # طريقة الدفع
                    payment_methods = {
                        'cash': 'نقدي',
                        'credit': 'آجل',
                        'bank_transfer': 'تحويل بنكي',
                        'check': 'شيك'
                    }
                    payment_method = payment_methods.get(sale.payment_method, sale.payment_method or "")
                    self.clients_sales_table.setItem(row, 4, QTableWidgetItem(payment_method))

                    # الحالة
                    statuses = {
                        'pending': 'في الانتظار',
                        'completed': 'مكتملة',
                        'cancelled': 'ملغية',
                        'returned': 'مرتجعة'
                    }
                    status_text = statuses.get(sale.status, sale.status or "")
                    self.clients_sales_table.setItem(row, 5, QTableWidgetItem(status_text))

            else:
                # تقرير جميع العملاء
                clients = self.session.query(Client).order_by(Client.name).all()

                # إنشاء تقرير HTML
                html_report = """
                <html>
                <head>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; }
                        .header { text-align: center; color: #2c3e50; margin-bottom: 20px; }
                        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
                        th, td { border: 1px solid #bdc3c7; padding: 8px; text-align: center; }
                        th { background-color: #34495e; color: white; }
                        .positive { color: #27ae60; font-weight: bold; }
                        .negative { color: #e74c3c; font-weight: bold; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h2>تقرير جميع العملاء</h2>
                    </div>
                    <table>
                        <tr>
                            <th>اسم العميل</th>
                            <th>عدد الفواتير</th>
                            <th>إجمالي المبيعات</th>
                            <th>إجمالي المدفوعات</th>
                            <th>المبلغ المتبقي</th>
                        </tr>
                """

                for client in clients:
                    sales = self.session.query(Sale).filter(Sale.client_id == client.id).all()
                    total_sales = sum(sale.total_amount for sale in sales)
                    total_paid = sum(sale.paid_amount for sale in sales)
                    remaining_amount = total_sales - total_paid

                    html_report += f"""
                        <tr>
                            <td>{client.name}</td>
                            <td>{len(sales)}</td>
                            <td class="positive">{format_currency(total_sales)}</td>
                            <td class="positive">{format_currency(total_paid)}</td>
                            <td class="{'negative' if remaining_amount > 0 else 'positive'}">{format_currency(remaining_amount)}</td>
                        </tr>
                    """

                html_report += """
                    </table>
                </body>
                </html>
                """

                self.clients_summary.setHtml(html_report)

                # إخفاء جدول المبيعات
                self.clients_sales_table.setRowCount(0)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنتاج تقرير العملاء: {str(e)}")

    def setup_suppliers_tab(self):
        """إعداد تبويب تقرير الموردين"""
        layout = QVBoxLayout()

        # مجموعة فلاتر التقرير
        filter_group = QGroupBox("فلاتر التقرير")
        filter_group
        filter_layout = QFormLayout()

        # فلتر المورد
        self.suppliers_supplier_combo = QComboBox()
        self.suppliers_supplier_combo.addItem("جميع الموردين", None)
        suppliers = self.session.query(Supplier).order_by(Supplier.name).all()
        for supplier in suppliers:
            self.suppliers_supplier_combo.addItem(supplier.name, supplier.id)
        filter_layout.addRow("المورد:", self.suppliers_supplier_combo)

        # زر تطبيق الفلاتر
        self.suppliers_apply_button = QPushButton("🏭 تطبيق التقرير")
        self.suppliers_apply_button.clicked.connect(self.generate_suppliers_report)
        self.suppliers_apply_button
        filter_layout.addRow("", self.suppliers_apply_button)

        filter_group.setLayout(filter_layout)

        # مجموعة نتائج التقرير
        results_group = QGroupBox("نتائج التقرير")
        results_group
        results_layout = QVBoxLayout()

        # تقرير الموردين
        self.suppliers_summary = QTextBrowser()
        self.suppliers_summary.setMinimumHeight(200)
        self.suppliers_summary

        # جدول مشتريات المورد
        self.suppliers_purchases_table = QTableWidget()
        self.suppliers_purchases_table.setColumnCount(5)
        self.suppliers_purchases_table.setHorizontalHeaderLabels(["رقم أمر الشراء", "التاريخ", "المبلغ الإجمالي", "المبلغ المدفوع", "الحالة"])
        self.suppliers_purchases_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.suppliers_purchases_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.suppliers_purchases_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.suppliers_purchases_table

        results_layout.addWidget(self.suppliers_summary)
        results_layout.addWidget(QLabel("مشتريات المورد:"))
        results_layout.addWidget(self.suppliers_purchases_table)

        results_group.setLayout(results_layout)

        layout.addWidget(filter_group)
        layout.addWidget(results_group)

        self.suppliers_tab.setLayout(layout)

    def generate_suppliers_report(self):
        """إنتاج تقرير الموردين"""
        try:
            # الحصول على الفلاتر
            supplier_id = self.suppliers_supplier_combo.currentData()

            if supplier_id:
                # تقرير مورد محدد
                supplier = self.session.query(Supplier).get(supplier_id)
                if not supplier:
                    show_error_message("خطأ", "المورد المحدد غير موجود")
                    return

                # الحصول على مشتريات المورد
                purchases = self.session.query(Purchase).filter(Purchase.supplier_id == supplier_id).order_by(Purchase.date.desc()).all()

                # حساب الإحصائيات
                total_purchases = sum(purchase.total_amount for purchase in purchases)
                total_paid = sum(purchase.paid_amount for purchase in purchases)
                remaining_amount = total_purchases - total_paid

                # إنشاء تقرير HTML
                html_report = f"""
                <html>
                <head>
                    <style>
                        body {{ font-family: Arial, sans-serif; direction: rtl; }}
                        .header {{ text-align: center; color: #2c3e50; margin-bottom: 20px; }}
                        .info {{ margin: 10px 0; }}
                        .positive {{ color: #27ae60; font-weight: bold; }}
                        .negative {{ color: #e74c3c; font-weight: bold; }}
                        .neutral {{ color: #34495e; font-weight: bold; }}
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h2>تقرير المورد: {supplier.name}</h2>
                    </div>
                    <div class="info">
                        <p><strong>الهاتف:</strong> {supplier.phone or 'غير محدد'}</p>
                        <p><strong>العنوان:</strong> {supplier.address or 'غير محدد'}</p>
                        <p><strong>عدد أوامر الشراء:</strong> <span class="neutral">{len(purchases)}</span></p>
                        <p><strong>إجمالي المشتريات:</strong> <span class="negative">{format_currency(total_purchases)}</span></p>
                        <p><strong>إجمالي المدفوعات:</strong> <span class="positive">{format_currency(total_paid)}</span></p>
                        <p><strong>المبلغ المتبقي:</strong> <span class="{'negative' if remaining_amount > 0 else 'positive'}">{format_currency(remaining_amount)}</span></p>
                    </div>
                </body>
                </html>
                """

                self.suppliers_summary.setHtml(html_report)

                # تحديث جدول المشتريات
                self.suppliers_purchases_table.setRowCount(len(purchases))
                for row, purchase in enumerate(purchases):
                    # رقم أمر الشراء
                    self.suppliers_purchases_table.setItem(row, 0, QTableWidgetItem(purchase.purchase_number or ""))

                    # التاريخ
                    date_str = purchase.date.strftime("%Y-%m-%d") if purchase.date else ""
                    self.suppliers_purchases_table.setItem(row, 1, QTableWidgetItem(date_str))

                    # المبلغ الإجمالي
                    self.suppliers_purchases_table.setItem(row, 2, QTableWidgetItem(format_currency(purchase.total_amount)))

                    # المبلغ المدفوع
                    self.suppliers_purchases_table.setItem(row, 3, QTableWidgetItem(format_currency(purchase.paid_amount)))

                    # الحالة
                    statuses = {
                        'pending': 'في الانتظار',
                        'partially_received': 'مستلم جزئياً',
                        'completed': 'مستلم بالكامل',
                        'cancelled': 'ملغي'
                    }
                    status_text = statuses.get(purchase.status, purchase.status or "")
                    self.suppliers_purchases_table.setItem(row, 4, QTableWidgetItem(status_text))

            else:
                # تقرير جميع الموردين
                suppliers = self.session.query(Supplier).order_by(Supplier.name).all()

                # إنشاء تقرير HTML
                html_report = """
                <html>
                <head>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; }
                        .header { text-align: center; color: #2c3e50; margin-bottom: 20px; }
                        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
                        th, td { border: 1px solid #bdc3c7; padding: 8px; text-align: center; }
                        th { background-color: #34495e; color: white; }
                        .positive { color: #27ae60; font-weight: bold; }
                        .negative { color: #e74c3c; font-weight: bold; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h2>تقرير جميع الموردين</h2>
                    </div>
                    <table>
                        <tr>
                            <th>اسم المورد</th>
                            <th>عدد أوامر الشراء</th>
                            <th>إجمالي المشتريات</th>
                            <th>إجمالي المدفوعات</th>
                            <th>المبلغ المتبقي</th>
                        </tr>
                """

                for supplier in suppliers:
                    purchases = self.session.query(Purchase).filter(Purchase.supplier_id == supplier.id).all()
                    total_purchases = sum(purchase.total_amount for purchase in purchases)
                    total_paid = sum(purchase.paid_amount for purchase in purchases)
                    remaining_amount = total_purchases - total_paid

                    html_report += f"""
                        <tr>
                            <td>{supplier.name}</td>
                            <td>{len(purchases)}</td>
                            <td class="negative">{format_currency(total_purchases)}</td>
                            <td class="positive">{format_currency(total_paid)}</td>
                            <td class="{'negative' if remaining_amount > 0 else 'positive'}">{format_currency(remaining_amount)}</td>
                        </tr>
                    """

                html_report += """
                    </table>
                </body>
                </html>
                """

                self.suppliers_summary.setHtml(html_report)

                # إخفاء جدول المشتريات
                self.suppliers_purchases_table.setRowCount(0)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنتاج تقرير الموردين: {str(e)}")

    def setup_expenses_tab(self):
        """إعداد تبويب تقرير المصروفات"""
        layout = QVBoxLayout()

        # مجموعة فلاتر التقرير
        filter_group = QGroupBox("فلاتر التقرير")
        filter_group
        filter_layout = QFormLayout()

        # فلتر الفترة الزمنية
        self.expenses_period_combo = QComboBox()
        self.expenses_period_combo.addItems(["اليوم", "الأسبوع الحالي", "الشهر الحالي", "الربع الحالي", "السنة الحالية", "فترة محددة"])
        self.expenses_period_combo.currentIndexChanged.connect(self.on_expenses_period_changed)
        filter_layout.addRow("الفترة الزمنية:", self.expenses_period_combo)

        # حقول تاريخ البداية والنهاية
        date_layout = QHBoxLayout()

        self.expenses_start_date = QDateEdit()
        self.expenses_start_date.setCalendarPopup(True)
        self.expenses_start_date.setDate(QDate.currentDate().addMonths(-1))
        self.expenses_start_date.setEnabled(False)

        self.expenses_end_date = QDateEdit()
        self.expenses_end_date.setCalendarPopup(True)
        self.expenses_end_date.setDate(QDate.currentDate())
        self.expenses_end_date.setEnabled(False)

        date_layout.addWidget(QLabel("من:"))
        date_layout.addWidget(self.expenses_start_date)
        date_layout.addWidget(QLabel("إلى:"))
        date_layout.addWidget(self.expenses_end_date)

        filter_layout.addRow("", date_layout)

        # فلتر نوع المصروف
        self.expenses_category_combo = QComboBox()
        self.expenses_category_combo.addItem("جميع الأنواع", None)
        # سنضيف الفئات من قاعدة البيانات
        try:
            categories = self.session.query(Expense.category).distinct().all()
            for category in categories:
                if category[0]:
                    self.expenses_category_combo.addItem(category[0], category[0])
        except:
            pass
        filter_layout.addRow("نوع المصروف:", self.expenses_category_combo)

        # زر تطبيق الفلاتر
        self.expenses_apply_button = QPushButton("💸 تطبيق التقرير")
        self.expenses_apply_button.clicked.connect(self.generate_expenses_report)
        self.expenses_apply_button
        filter_layout.addRow("", self.expenses_apply_button)

        filter_group.setLayout(filter_layout)

        # مجموعة نتائج التقرير
        results_group = QGroupBox("نتائج التقرير")
        results_group
        results_layout = QVBoxLayout()

        # ملخص المصروفات
        summary_layout = QHBoxLayout()

        self.expenses_count_label = QLabel("عدد المصروفات: 0")
        self.expenses_count_label

        self.expenses_total_label = QLabel("إجمالي المصروفات: 0.00")
        self.expenses_total_label

        summary_layout.addWidget(self.expenses_count_label)
        summary_layout.addWidget(self.expenses_total_label)

        # جدول المصروفات
        self.expenses_table = QTableWidget()
        self.expenses_table.setColumnCount(5)
        self.expenses_table.setHorizontalHeaderLabels(["التاريخ", "الوصف", "النوع", "المبلغ", "الملاحظات"])
        self.expenses_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.expenses_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.expenses_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.expenses_table

        results_layout.addLayout(summary_layout)
        results_layout.addWidget(self.expenses_table)

        results_group.setLayout(results_layout)

        layout.addWidget(filter_group)
        layout.addWidget(results_group)

        self.expenses_tab.setLayout(layout)

    def generate_expenses_report(self):
        """إنتاج تقرير المصروفات"""
        try:
            # الحصول على الفلاتر
            start_date = qdate_to_datetime(self.expenses_start_date.date())
            end_date = qdate_to_datetime(self.expenses_end_date.date())
            category = self.expenses_category_combo.currentData()

            # بناء الاستعلام
            query = self.session.query(Expense).filter(
                Expense.date >= start_date,
                Expense.date <= end_date
            )

            # تطبيق فلتر النوع
            if category:
                query = query.filter(Expense.category == category)

            # تنفيذ الاستعلام
            expenses = query.order_by(Expense.date.desc()).all()

            # حساب الإحصائيات
            total_expenses = sum(expense.amount for expense in expenses)

            # تحديث الملخص
            self.expenses_count_label.label.setText(f"عدد المصروفات: {len(expenses)}")
            self.expenses_total_label.label.setText(f"إجمالي المصروفات: {format_currency(total_expenses)}")

            # تحديث الجدول
            self.expenses_table.setRowCount(len(expenses))
            for row, expense in enumerate(expenses):
                # التاريخ
                date_str = expense.date.strftime("%Y-%m-%d") if expense.date else ""
                self.expenses_table.setItem(row, 0, QTableWidgetItem(date_str))

                # الوصف
                self.expenses_table.setItem(row, 1, QTableWidgetItem(expense.description or ""))

                # النوع
                self.expenses_table.setItem(row, 2, QTableWidgetItem(expense.category or ""))

                # المبلغ
                self.expenses_table.setItem(row, 3, QTableWidgetItem(format_currency(expense.amount)))

                # الملاحظات
                self.expenses_table.setItem(row, 4, QTableWidgetItem(expense.notes or ""))

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنتاج تقرير المصروفات: {str(e)}")

    def setup_revenues_tab(self):
        """إعداد تبويب تقرير الإيرادات"""
        layout = QVBoxLayout()

        # مجموعة فلاتر التقرير
        filter_group = QGroupBox("فلاتر التقرير")
        filter_group
        filter_layout = QFormLayout()

        # فلتر الفترة الزمنية
        self.revenues_period_combo = QComboBox()
        self.revenues_period_combo.addItems(["اليوم", "الأسبوع الحالي", "الشهر الحالي", "الربع الحالي", "السنة الحالية", "فترة محددة"])
        self.revenues_period_combo.currentIndexChanged.connect(self.on_revenues_period_changed)
        filter_layout.addRow("الفترة الزمنية:", self.revenues_period_combo)

        # حقول تاريخ البداية والنهاية
        date_layout = QHBoxLayout()

        self.revenues_start_date = QDateEdit()
        self.revenues_start_date.setCalendarPopup(True)
        self.revenues_start_date.setDate(QDate.currentDate().addMonths(-1))
        self.revenues_start_date.setEnabled(False)

        self.revenues_end_date = QDateEdit()
        self.revenues_end_date.setCalendarPopup(True)
        self.revenues_end_date.setDate(QDate.currentDate())
        self.revenues_end_date.setEnabled(False)

        date_layout.addWidget(QLabel("من:"))
        date_layout.addWidget(self.revenues_start_date)
        date_layout.addWidget(QLabel("إلى:"))
        date_layout.addWidget(self.revenues_end_date)

        filter_layout.addRow("", date_layout)

        # فلتر نوع الإيراد
        self.revenues_category_combo = QComboBox()
        self.revenues_category_combo.addItem("جميع الأنواع", None)
        # سنضيف الفئات من قاعدة البيانات
        try:
            categories = self.session.query(Revenue.category).distinct().all()
            for category in categories:
                if category[0]:
                    self.revenues_category_combo.addItem(category[0], category[0])
        except:
            pass
        filter_layout.addRow("نوع الإيراد:", self.revenues_category_combo)

        # زر تطبيق الفلاتر
        self.revenues_apply_button = QPushButton("💰 تطبيق التقرير")
        self.revenues_apply_button.clicked.connect(self.generate_revenues_report)
        self.revenues_apply_button
        filter_layout.addRow("", self.revenues_apply_button)

        filter_group.setLayout(filter_layout)

        # مجموعة نتائج التقرير
        results_group = QGroupBox("نتائج التقرير")
        results_group
        results_layout = QVBoxLayout()

        # ملخص الإيرادات
        summary_layout = QHBoxLayout()

        self.revenues_count_label = QLabel("عدد الإيرادات: 0")
        self.revenues_count_label

        self.revenues_total_label = QLabel("إجمالي الإيرادات: 0.00")
        self.revenues_total_label

        summary_layout.addWidget(self.revenues_count_label)
        summary_layout.addWidget(self.revenues_total_label)

        # جدول الإيرادات
        self.revenues_table = QTableWidget()
        self.revenues_table.setColumnCount(5)
        self.revenues_table.setHorizontalHeaderLabels(["التاريخ", "الوصف", "النوع", "المبلغ", "الملاحظات"])
        self.revenues_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.revenues_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.revenues_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.revenues_table

        results_layout.addLayout(summary_layout)
        results_layout.addWidget(self.revenues_table)

        results_group.setLayout(results_layout)

        layout.addWidget(filter_group)
        layout.addWidget(results_group)

        self.revenues_tab.setLayout(layout)

    def generate_revenues_report(self):
        """إنتاج تقرير الإيرادات"""
        try:
            # الحصول على الفلاتر
            start_date = qdate_to_datetime(self.revenues_start_date.date())
            end_date = qdate_to_datetime(self.revenues_end_date.date())
            category = self.revenues_category_combo.currentData()

            # بناء الاستعلام
            query = self.session.query(Revenue).filter(
                Revenue.date >= start_date,
                Revenue.date <= end_date
            )

            # تطبيق فلتر النوع
            if category:
                query = query.filter(Revenue.category == category)

            # تنفيذ الاستعلام
            revenues = query.order_by(Revenue.date.desc()).all()

            # حساب الإحصائيات
            total_revenues = sum(revenue.amount for revenue in revenues)

            # تحديث الملخص
            self.revenues_count_label.label.setText(f"عدد الإيرادات: {len(revenues)}")
            self.revenues_total_label.label.setText(f"إجمالي الإيرادات: {format_currency(total_revenues)}")

            # تحديث الجدول
            self.revenues_table.setRowCount(len(revenues))
            for row, revenue in enumerate(revenues):
                # التاريخ
                date_str = revenue.date.strftime("%Y-%m-%d") if revenue.date else ""
                self.revenues_table.setItem(row, 0, QTableWidgetItem(date_str))

                # الوصف
                self.revenues_table.setItem(row, 1, QTableWidgetItem(revenue.description or ""))

                # النوع
                self.revenues_table.setItem(row, 2, QTableWidgetItem(revenue.category or ""))

                # المبلغ
                self.revenues_table.setItem(row, 3, QTableWidgetItem(format_currency(revenue.amount)))

                # الملاحظات
                self.revenues_table.setItem(row, 4, QTableWidgetItem(revenue.notes or ""))

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنتاج تقرير الإيرادات: {str(e)}")

    def setup_projects_tab(self):
        """إعداد تبويب تقرير المشاريع"""
        layout = QVBoxLayout()

        # مجموعة فلاتر التقرير
        filter_group = QGroupBox("فلاتر التقرير")
        filter_group
        filter_layout = QFormLayout()

        # فلتر المشروع
        self.projects_project_combo = QComboBox()
        self.projects_project_combo.addItem("جميع المشاريع", None)
        try:
            projects = self.session.query(Project).order_by(Project.name).all()
            for project in projects:
                self.projects_project_combo.addItem(project.name, project.id)
        except:
            pass
        filter_layout.addRow("المشروع:", self.projects_project_combo)

        # فلتر حالة المشروع
        self.projects_status_combo = QComboBox()
        self.projects_status_combo.addItem("جميع الحالات", None)
        self.projects_status_combo.addItem("قيد التخطيط", "planning")
        self.projects_status_combo.addItem("قيد التنفيذ", "in_progress")
        self.projects_status_combo.addItem("مكتمل", "completed")
        self.projects_status_combo.addItem("متوقف", "on_hold")
        self.projects_status_combo.addItem("ملغي", "cancelled")
        filter_layout.addRow("حالة المشروع:", self.projects_status_combo)

        # زر تطبيق الفلاتر
        self.projects_apply_button = QPushButton("🏗️ تطبيق التقرير")
        self.projects_apply_button.clicked.connect(self.generate_projects_report)
        self.projects_apply_button
        filter_layout.addRow("", self.projects_apply_button)

        filter_group.setLayout(filter_layout)

        # مجموعة نتائج التقرير
        results_group = QGroupBox("نتائج التقرير")
        results_group
        results_layout = QVBoxLayout()

        # ملخص المشاريع
        summary_layout = QHBoxLayout()

        self.projects_count_label = QLabel("عدد المشاريع: 0")
        self.projects_count_label

        self.projects_budget_label = QLabel("إجمالي الميزانية: 0.00")
        self.projects_budget_label

        summary_layout.addWidget(self.projects_count_label)
        summary_layout.addWidget(self.projects_budget_label)

        # جدول المشاريع
        self.projects_table = QTableWidget()
        self.projects_table.setColumnCount(6)
        self.projects_table.setHorizontalHeaderLabels(["اسم المشروع", "العميل", "تاريخ البداية", "تاريخ النهاية", "الميزانية", "الحالة"])
        self.projects_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.projects_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.projects_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.projects_table

        results_layout.addLayout(summary_layout)
        results_layout.addWidget(self.projects_table)

        results_group.setLayout(results_layout)

        layout.addWidget(filter_group)
        layout.addWidget(results_group)

        self.projects_tab.setLayout(layout)

    def generate_projects_report(self):
        """إنتاج تقرير المشاريع"""
        try:
            # الحصول على الفلاتر
            project_id = self.projects_project_combo.currentData()
            status = self.projects_status_combo.currentData()

            # بناء الاستعلام
            query = self.session.query(Project)

            # تطبيق فلتر المشروع
            if project_id:
                query = query.filter(Project.id == project_id)

            # تطبيق فلتر الحالة
            if status:
                query = query.filter(Project.status == status)

            # تنفيذ الاستعلام
            projects = query.order_by(Project.name).all()

            # حساب الإحصائيات
            total_budget = sum(project.budget or 0 for project in projects)

            # تحديث الملخص
            self.projects_count_label.label.setText(f"عدد المشاريع: {len(projects)}")
            self.projects_budget_label.label.setText(f"إجمالي الميزانية: {format_currency(total_budget)}")

            # تحديث الجدول
            self.projects_table.setRowCount(len(projects))
            for row, project in enumerate(projects):
                # اسم المشروع
                self.projects_table.setItem(row, 0, QTableWidgetItem(project.name or ""))

                # العميل
                client_name = ""
                if project.client_id:
                    try:
                        client = self.session.query(Client).get(project.client_id)
                        if client:
                            client_name = client.name
                    except:
                        pass
                self.projects_table.setItem(row, 1, QTableWidgetItem(client_name))

                # تاريخ البداية
                start_date_str = project.start_date.strftime("%Y-%m-%d") if project.start_date else ""
                self.projects_table.setItem(row, 2, QTableWidgetItem(start_date_str))

                # تاريخ النهاية
                end_date_str = project.end_date.strftime("%Y-%m-%d") if project.end_date else ""
                self.projects_table.setItem(row, 3, QTableWidgetItem(end_date_str))

                # الميزانية
                self.projects_table.setItem(row, 4, QTableWidgetItem(format_currency(project.budget or 0)))

                # الحالة
                statuses = {
                    'planning': 'قيد التخطيط',
                    'in_progress': 'قيد التنفيذ',
                    'completed': 'مكتمل',
                    'on_hold': 'متوقف',
                    'cancelled': 'ملغي'
                }
                status_text = statuses.get(project.status, project.status or "")
                self.projects_table.setItem(row, 5, QTableWidgetItem(status_text))

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنتاج تقرير المشاريع: {str(e)}")

    def export_to_csv(self, table, filename):
        """تصدير جدول إلى ملف CSV"""
        try:
            # فتح حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ التقرير",
                f"{filename}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة رؤوس الأعمدة
                    headers = []
                    for col in range(table.columnCount()):
                        headers.append(table.horizontalHeaderItem(col).text())
                    writer.writerow(headers)

                    # كتابة البيانات
                    for row in range(table.rowCount()):
                        row_data = []
                        for col in range(table.columnCount()):
                            item = table.item(row, col)
                            row_data.append(item.text() if item else "")
                        writer.writerow(row_data)

                QMessageBox.information(self, "نجح التصدير", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ في التصدير", f"حدث خطأ أثناء تصدير التقرير: {str(e)}")

    def print_table(self, table, title):
        """طباعة جدول"""
        try:
            printer = QPrinter()
            print_dialog = QPrintDialog(printer, self)

            if print_dialog.exec_() == QPrintDialog.Accepted:
                # إنشاء HTML للطباعة
                html = f"""
                <html>
                <head>
                    <meta charset="utf-8">
                    <style>
                        body {{ font-family: Arial, sans-serif; direction: rtl; }}
                        h1 {{ text-align: center; color: #2c3e50; }}
                        table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                        th, td {{ border: 1px solid #bdc3c7; padding: 8px; text-align: center; }}
                        th {{ background-color: #34495e; color: white; }}
                        tr:nth-child(even) {{ background-color: #f8f9fa; }}
                    </style>
                </head>
                <body>
                    <h1>{title}</h1>
                    <table>
                        <tr>
                """

                # إضافة رؤوس الأعمدة
                for col in range(table.columnCount()):
                    header = table.horizontalHeaderItem(col).text()
                    html += f"<th>{header}</th>"
                html += "</tr>"

                # إضافة البيانات
                for row in range(table.rowCount()):
                    html += "<tr>"
                    for col in range(table.columnCount()):
                        item = table.item(row, col)
                        cell_text = item.text() if item else ""
                        html += f"<td>{cell_text}</td>"
                    html += "</tr>"

                html += """
                    </table>
                </body>
                </html>
                """

                # طباعة HTML
                from PyQt5.QtWidgets import QTextDocument
                document = QTextDocument()
                document.setHtml(html)
                document.print_(printer)

                QMessageBox.information(self, "نجحت الطباعة", "تم إرسال التقرير للطباعة بنجاح")

        except Exception as e:
            show_error_message("خطأ في الطباعة", f"حدث خطأ أثناء طباعة التقرير: {str(e)}")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر
            colors = {
                'primary': {
                    'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #3b82f6, stop:0.5 #2563eb, stop:1 #1d4ed8)',
                    'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #60a5fa, stop:0.5 #3b82f6, stop:1 #2563eb)',
                    'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #1d4ed8, stop:1 #1e40af)',
                    'border': '#1e40af'
                },
                'emerald': {
                    'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #10b981, stop:0.5 #059669, stop:1 #047857)',
                    'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #34d399, stop:0.5 #10b981, stop:1 #059669)',
                    'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #047857, stop:1 #065f46)',
                    'border': '#065f46'
                },
                'warning': {
                    'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f59e0b, stop:0.5 #d97706, stop:1 #b45309)',
                    'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #fbbf24, stop:0.5 #f59e0b, stop:1 #d97706)',
                    'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #b45309, stop:1 #92400e)',
                    'border': '#92400e'
                },
                'info': {
                    'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #06b6d4, stop:0.5 #0891b2, stop:1 #0e7490)',
                    'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #67e8f9, stop:0.5 #06b6d4, stop:1 #0891b2)',
                    'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #0e7490, stop:1 #155e75)',
                    'border': '#155e75'
                },
                'rose': {
                    'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f43f5e, stop:0.5 #e11d48, stop:1 #be123c)',
                    'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #fb7185, stop:0.5 #f43f5e, stop:1 #e11d48)',
                    'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #be123c, stop:1 #9f1239)',
                    'border': '#9f1239'
                },
                'indigo': {
                    'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #6366f1, stop:0.5 #4f46e5, stop:1 #4338ca)',
                    'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #a5b4fc, stop:0.5 #6366f1, stop:1 #4f46e5)',
                    'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #4338ca, stop:1 #3730a3)',
                    'border': '#3730a3'
                },
                'modern_teal': {
                    'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #14b8a6, stop:0.5 #0d9488, stop:1 #0f766e)',
                    'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #5eead4, stop:0.5 #14b8a6, stop:1 #0d9488)',
                    'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #0f766e, stop:1 #134e4a)',
                    'border': '#134e4a'
                }
            }

            # الحصول على ألوان الزر
            color_scheme = colors.get(button_type, colors['primary'])

            # تطبيق التصميم المتطور
            style = f"""
                QPushButton {{
                    background: {color_scheme['normal']};
                    color: #ffffff;
                    border: 3px solid {color_scheme['border']};
                    border-radius: 12px;
                    padding: 10px 20px;
                    font-size: 14px;
                    font-weight: bold;
                    min-height: 35px;
                    max-height: 45px;
                }}
                QPushButton:hover {{
                    background: {color_scheme['hover']};
                    border: 3px solid {color_scheme['border']};
                }}
                QPushButton:pressed {{
                    background: {color_scheme['pressed']};
                    border: 3px solid {color_scheme['border']};
                }}
                QPushButton:disabled {{
                    background: #9ca3af;
                    color: #6b7280;
                    border: 3px solid #6b7280;
                }}
            """

            button.setStyleSheet(style)
            print(f"✅ تم تطبيق التصميم المتطور على الزر: {button.text()} - نوع: {button_type}")

        except Exception as e:
            print(f"❌ خطأ في تطبيق تصميم الزر: {str(e)}")

    def export_to_excel(self):
        """تصدير التقرير الحالي إلى Excel"""
        try:
            current_tab = self.tabs.currentIndex()
            if current_tab == 0:  # تقرير المبيعات
                self.export_to_csv(self.sales_table, "تقرير_المبيعات")
            elif current_tab == 1:  # تقرير المشتريات
                self.export_to_csv(self.purchases_table, "تقرير_المشتريات")
            elif current_tab == 3:  # تقرير المخزون
                self.export_to_csv(self.inventory_table, "تقرير_المخزون")
            elif current_tab == 4:  # تقرير العملاء
                self.export_to_csv(self.clients_sales_table, "تقرير_العملاء")
            else:
                QMessageBox.information(self, "تنبيه", "يرجى اختيار تقرير قابل للتصدير")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء التصدير: {str(e)}")

    def export_to_pdf(self):
        """تصدير التقرير الحالي إلى PDF"""
        try:
            current_tab = self.tabs.currentIndex()
            if current_tab == 0:  # تقرير المبيعات
                self.print_table(self.sales_table, "تقرير المبيعات")
            elif current_tab == 1:  # تقرير المشتريات
                self.print_table(self.purchases_table, "تقرير المشتريات")
            elif current_tab == 3:  # تقرير المخزون
                self.print_table(self.inventory_table, "تقرير المخزون")
            elif current_tab == 4:  # تقرير العملاء
                self.print_table(self.clients_sales_table, "تقرير العملاء")
            else:
                QMessageBox.information(self, "تنبيه", "يرجى اختيار تقرير قابل للطباعة")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء التصدير إلى PDF: {str(e)}")

    def export_to_csv(self, table, filename):
        """تصدير جدول إلى ملف CSV"""
        try:
            # فتح حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ التقرير",
                f"{filename}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة رؤوس الأعمدة
                    headers = []
                    for col in range(table.columnCount()):
                        headers.append(table.horizontalHeaderItem(col).text())
                    writer.writerow(headers)

                    # كتابة البيانات
                    for row in range(table.rowCount()):
                        row_data = []
                        for col in range(table.columnCount()):
                            item = table.item(row, col)
                            row_data.append(item.text() if item else "")
                        writer.writerow(row_data)

                QMessageBox.information(self, "نجح التصدير", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ في التصدير", f"حدث خطأ أثناء تصدير التقرير: {str(e)}")

    def refresh_all_reports(self):
        """تحديث جميع التقارير"""
        try:
            # تحديث تقرير المبيعات
            self.generate_sales_report()
            # تحديث تقرير المشتريات
            self.generate_purchases_report()
            # تحديث تقرير الأرباح والخسائر
            self.generate_profit_loss_report()
            # تحديث تقرير المخزون
            self.generate_inventory_report()
            # تحديث تقرير العملاء
            self.generate_clients_report()

            QMessageBox.information(self, "تم التحديث", "تم تحديث جميع التقارير بنجاح")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تحديث التقارير: {str(e)}")
