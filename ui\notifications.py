from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                            QHeaderView, QMessageBox, QDialog, QTextEdit, QComboBox,
                            QTabWidget, QSizePolicy, QFrame)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QIcon, QFont, QColor

from database import Notification, Invoice, Client, Reminder
from utils import show_error_message, show_info_message, show_confirmation_message, format_datetime
import datetime

from ui.unified_styles import (UnifiedStyles, StyledButton, StyledGroupBox,
                                StyledTable, StyledLabel, StyledTabWidget)

class NotificationsWidget(QWidget):
    """واجهة إدارة الإشعارات والتنبيهات"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.init_ui()
        self.refresh_data()

    def init_ui(self):
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(5, 5, 5, 5)  # هوامش صغيرة
        main_layout.setSpacing(5)

        # إنشاء تبويبات للإشعارات والتنبيهات مع تحسين العرض
        styled_tabs = StyledTabWidget()
        self.tabs = styled_tabs.tab_widget

        # تحسين تصميم التبويبات مع إطار أسود وعرض أكبر 3 أضعاف
        self.tabs.setStyleSheet("""
            QTabWidget {
                border: 3px solid #000000;
                border-radius: 8px;
                background-color: #f8fafc;
                font-size: 14px;
                font-weight: bold;
            }
            QTabWidget::pane {
                border: 3px solid #000000;
                border-radius: 8px;
                background-color: #ffffff;
                top: -3px;
            }
            QTabBar {
                alignment: center;
            }
            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e2e8f0,
                    stop:0.5 #cbd5e1,
                    stop:1 #94a3b8);
                color: #1e293b;
                border: 3px solid #000000;
                border-bottom: none;
                border-radius: 8px 8px 0 0;
                padding: 8px 32px;
                margin: 2px;
                font-size: 14px;
                font-weight: bold;
                min-width: 800px;
                max-width: 1200px;
                height: 35px;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3b82f6,
                    stop:0.5 #2563eb,
                    stop:1 #1d4ed8);
                color: #ffffff;
                border: 3px solid #000000;
                border-bottom: 3px solid #ffffff;
                margin-bottom: -3px;
                height: 35px;
            }
            QTabBar::tab:hover:!selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f1f5f9,
                    stop:0.5 #e2e8f0,
                    stop:1 #cbd5e1);
                border: 3px solid #000000;
            }
        """)

        # إنشاء تبويب الإشعارات
        self.notifications_tab = QWidget()
        self.init_notifications_tab()

        # إضافة تبويب الإشعارات إلى التبويبات
        self.tabs.addTab(self.notifications_tab, "🔔 الإشعارات")

        # إنشاء تبويب التنبيهات
        from ui.reminders import RemindersWidget
        self.reminders_widget = RemindersWidget(self.session)
        self.tabs.addTab(self.reminders_widget, "⏰ التنبيهات")

        # تعيين سياسة الحجم للتبويبات لتأخذ العرض الكامل
        self.tabs.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # إضافة التبويبات إلى التخطيط الرئيسي
        main_layout.addWidget(self.tabs)

        self.setLayout(main_layout)

    def init_notifications_tab(self):
        """تهيئة تبويب الإشعارات"""
        # إنشاء التخطيط الرئيسي لتبويب الإشعارات مع تحسين العرض الكامل
        notifications_layout = QVBoxLayout(self.notifications_tab)
        notifications_layout.setContentsMargins(5, 5, 5, 5)  # هوامش أصغر لاستغلال المساحة
        notifications_layout.setSpacing(5)

        # تعيين سياسة الحجم للتبويب ليأخذ العرض الكامل
        self.notifications_tab.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # إضافة العنوان الرئيسي المحسن مع ارتفاع أقل
        title_label = QLabel("🔔 إدارة الإشعارات المتطورة")
        title_label.setFont(QFont("Segoe UI", 14, QFont.Bold))  # خط أصغر لتوفير المساحة
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e40af,
                    stop:0.2 #3b82f6,
                    stop:0.4 #6366f1,
                    stop:0.6 #8b5cf6,
                    stop:0.8 #a855f7,
                    stop:1 #c084fc);
                border: 2px solid #1d4ed8;
                border-radius: 8px;
                padding: 6px 12px;
                margin: 2px;
                font-weight: bold;
                max-height: 32px;
                min-height: 32px;
            }
        """)
        notifications_layout.addWidget(title_label)

        # إنشاء إطار علوي محسن مع إطار أسود
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e1);
                border: 3px solid #000000;
                border-radius: 8px;
                margin: 1px;
                padding: 2px;
                max-height: 55px;
                min-height: 50px;
            }
        """)
        # تعيين سياسة الحجم للإطار العلوي
        top_frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        # تخطيط أفقي محسن مع مساحة أكبر للعناصر
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(5, 5, 5, 5)  # هوامش صغيرة
        search_layout.setSpacing(6)  # مسافات مناسبة بين العناصر

        # إنشاء حاوي عمودي للتوسيط مع تحسين المساحة
        top_container = QVBoxLayout()
        top_container.setContentsMargins(3, 3, 3, 3)  # هوامش صغيرة
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط (أقل)
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط (أقل)
        top_container.addStretch(1)

        # تسمية البحث محسنة مع حجم أصغر
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                padding: 6px 10px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #dc2626,
                    stop:0.5 #b91c1c,
                    stop:1 #991b1b);
                border: 2px solid #7f1d1d;
                border-radius: 8px;
                min-width: 60px;
                max-width: 60px;
                max-height: 30px;
                min-height: 28px;
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ef4444,
                    stop:0.5 #dc2626,
                    stop:1 #b91c1c);
                border: 2px solid #ef4444;
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث بالعنوان أو الرسالة...")
        self.search_edit.textChanged.connect(self.filter_notifications)
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff,
                    stop:0.5 #f8fafc,
                    stop:1 #e2e8f0);
                border: 2px solid #4f46e5;
                border-radius: 8px;
                padding: 6px 12px;
                font-size: 12px;
                font-weight: bold;
                color: #1f2937;
                max-height: 30px;
                min-height: 28px;
                selection-background-color: #4f46e5;
            }
            QLineEdit:focus {
                border: 2px solid #3730a3;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f0f9ff,
                    stop:1 #e0f2fe);
            }
            QLineEdit:hover {
                border: 2px solid #5b52f0;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #fafbff,
                    stop:1 #f1f5f9);
            }
        """)
        # تعيين سياسة الحجم لحقل البحث ليأخذ المساحة المتاحة
        self.search_edit.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        search_button = QPushButton("🔍")
        search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #0891b2,
                    stop:0.5 #0e7490,
                    stop:1 #155e75);
                color: #ffffff;
                border: 2px solid #164e63;
                border-radius: 8px;
                padding: 6px;
                font-size: 16px;
                font-weight: bold;
                min-width: 40px;
                max-width: 40px;
                max-height: 30px;
                min-height: 28px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #22d3ee,
                    stop:0.5 #0891b2,
                    stop:1 #0e7490);
                border: 2px solid #22d3ee;
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #0e7490,
                    stop:1 #155e75);
                border: 2px solid #0e7490;
            }
        """)
        search_button.clicked.connect(self.filter_notifications)
        search_button.setToolTip("بحث متقدم")
        search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        search_button.setContentsMargins(0, 0, 0, 0)

        # تسمية النوع محسنة مع حجم أصغر
        type_label = QLabel("📋 نوع:")
        type_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                padding: 6px 10px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f59e0b,
                    stop:0.5 #d97706,
                    stop:1 #b45309);
                border: 2px solid #92400e;
                border-radius: 8px;
                min-width: 55px;
                max-width: 55px;
                max-height: 30px;
                min-height: 28px;
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #fbbf24,
                    stop:0.5 #f59e0b,
                    stop:1 #d97706);
                border: 2px solid #fbbf24;
            }
        """)
        type_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        # إضافة حقل تصفية حسب النوع محسن
        self.type_filter = QComboBox()
        self.type_filter.addItem("جميع الأنواع", None)
        self.type_filter.addItem("فواتير مستحقة", "invoice_due")
        self.type_filter.addItem("تنبيهات مستحقة", "reminder_due")
        self.type_filter.currentIndexChanged.connect(self.filter_notifications)
        self.type_filter.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff,
                    stop:0.5 #f8fafc,
                    stop:1 #e2e8f0);
                border: 2px solid #4f46e5;
                border-radius: 8px;
                padding: 6px 12px;
                font-size: 12px;
                font-weight: bold;
                color: #1f2937;
                max-height: 30px;
                min-height: 28px;
                min-width: 100px;
            }
            QComboBox:hover {
                border: 2px solid #5b52f0;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #fafbff,
                    stop:1 #f1f5f9);
            }
            QComboBox::drop-down {
                border: none;
                width: 25px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 2px solid #4f46e5;
                width: 6px;
                height: 6px;
                border-radius: 3px;
                background: #4f46e5;
            }
        """)
        self.type_filter.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)

        # تسمية الحالة محسنة
        status_label = QLabel("🎯 حالة:")
        status_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                padding: 6px 10px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #dc2626,
                    stop:0.5 #b91c1c,
                    stop:1 #991b1b);
                border: 2px solid #7f1d1d;
                border-radius: 8px;
                min-width: 55px;
                max-width: 55px;
                max-height: 30px;
                min-height: 28px;
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ef4444,
                    stop:0.5 #dc2626,
                    stop:1 #b91c1c);
                border: 2px solid #ef4444;
            }
        """)
        status_label.setAlignment(Qt.AlignCenter)

        # إضافة حقل تصفية حسب الحالة محسن
        self.read_filter = QComboBox()
        self.read_filter.addItem("الكل", None)
        self.read_filter.addItem("غير مقروءة", False)
        self.read_filter.addItem("مقروءة", True)
        self.read_filter.currentIndexChanged.connect(self.filter_notifications)
        self.read_filter.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff,
                    stop:0.5 #f8fafc,
                    stop:1 #e2e8f0);
                border: 2px solid #4f46e5;
                border-radius: 8px;
                padding: 6px 12px;
                font-size: 12px;
                font-weight: bold;
                color: #1f2937;
                max-height: 30px;
                min-height: 28px;
                min-width: 100px;
            }
            QComboBox:hover {
                border: 2px solid #5b52f0;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #fafbff,
                    stop:1 #f1f5f9);
            }
            QComboBox::drop-down {
                border: none;
                width: 25px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 2px solid #4f46e5;
                width: 6px;
                height: 6px;
                border-radius: 3px;
                background: #4f46e5;
            }
        """)
        self.read_filter.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)

        # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 3, Qt.AlignVCenter)  # يأخذ مساحة أكبر
        search_layout.addWidget(search_button, 0, Qt.AlignVCenter)
        search_layout.addWidget(type_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.type_filter, 1, Qt.AlignVCenter)
        search_layout.addWidget(status_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.read_filter, 1, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        top_frame.setLayout(top_container)

        # إنشاء جدول الإشعارات المتطور والمحسن
        self.create_advanced_notifications_table()

        notifications_layout.addWidget(top_frame)
        notifications_layout.addWidget(self.notifications_table, 1)  # إعطاء الجدول أولوية في التمدد

        # تعيين سياسة الحجم للجدول ليأخذ العرض والارتفاع المتاح
        self.notifications_table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # إنشاء إطار سفلي للأزرار مع إطار أسود
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e1);
                border: 3px solid #000000;
                border-radius: 8px;
                margin: 1px;
                padding: 2px;
                max-height: 60px;
                min-height: 55px;
            }
        """)
        # تعيين سياسة الحجم للإطار السفلي
        bottom_frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        actions_layout.setSpacing(4)  # مسافة أكبر بين الأزرار لتوزيع أفضل

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # إنشاء الأزرار بأحجام محسنة وأكثر إحكاماً

        # المجموعة الأولى - العمليات الأساسية مع ألوان متنوعة
        self.view_button = QPushButton("👁️ عرض التفاصيل ▼")
        self.style_advanced_button(self.view_button, 'indigo', has_menu=True)  # بنفسجي للتفاصيل مع قائمة
        self.view_button.clicked.connect(self.view_notification)
        self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.mark_read_button = QPushButton("✅ تعليم كمقروء")
        self.style_advanced_button(self.mark_read_button, 'emerald')  # أخضر للقراءة
        self.mark_read_button.clicked.connect(self.mark_as_read)
        self.mark_read_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.mark_unread_button = QPushButton("❌ تعليم كغير مقروء")
        self.style_advanced_button(self.mark_unread_button, 'orange')  # برتقالي لعدم القراءة
        self.mark_unread_button.clicked.connect(self.mark_as_unread)
        self.mark_unread_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.delete_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_button, 'danger')  # أحمر تحذيري
        self.delete_button.clicked.connect(self.delete_notification)
        self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # المجموعة الثانية - العمليات المتقدمة
        self.check_due_invoices_button = QPushButton("📋 فواتير مستحقة")
        self.style_advanced_button(self.check_due_invoices_button, 'info')  # أزرق للفواتير
        self.check_due_invoices_button.clicked.connect(self.check_due_invoices)
        self.check_due_invoices_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.check_due_reminders_button = QPushButton("⏰ تنبيهات مستحقة")
        self.style_advanced_button(self.check_due_reminders_button, 'cyan')  # سماوي للتنبيهات
        self.check_due_reminders_button.clicked.connect(self.check_due_reminders)
        self.check_due_reminders_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_button, 'modern_teal')  # تصميم حديث ومتطور
        self.refresh_button.clicked.connect(self.refresh_data)
        self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.statistics_button = QPushButton("📊 الإحصائيات")
        self.style_advanced_button(self.statistics_button, 'rose')  # وردي للإحصائيات
        self.statistics_button.clicked.connect(self.show_statistics)
        self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إضافة ملخص الإشعارات محسن مثل التنبيهات
        self.total_label = QLabel("إجمالي الإشعارات: 0")
        self.total_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                padding: 8px 16px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #059669,
                    stop:0.5 #047857,
                    stop:1 #065f46);
                border: 3px solid #064e3b;
                border-radius: 12px;
                min-height: 34px;
                max-height: 38px;
            }
        """)
        self.total_label.setAlignment(Qt.AlignCenter)

        # إضافة الأزرار للتخطيط
        actions_layout.addWidget(self.view_button)
        actions_layout.addWidget(self.mark_read_button)
        actions_layout.addWidget(self.mark_unread_button)
        actions_layout.addWidget(self.delete_button)
        actions_layout.addWidget(self.check_due_invoices_button)
        actions_layout.addWidget(self.check_due_reminders_button)
        actions_layout.addWidget(self.refresh_button)
        actions_layout.addWidget(self.statistics_button)
        actions_layout.addWidget(self.total_label)

        # تعيين التخطيط للإطار السفلي
        bottom_frame.setLayout(bottom_container)

        # تجميع التخطيط النهائي
        notifications_layout.addWidget(bottom_frame)

    def create_advanced_notifications_table(self):
        """إنشاء جدول الإشعارات المتطور والمحسن مطابق للموردين"""
        styled_table = StyledTable()
        self.notifications_table = styled_table.table
        self.notifications_table.setColumnCount(5)
        self.notifications_table.setHorizontalHeaderLabels(["الرقم", "العنوان", "التاريخ", "النوع", "الحالة"])

        # تحسين عرض الأعمدة
        header = self.notifications_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # الرقم
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # العنوان
        header.setSectionResizeMode(2, QHeaderView.Fixed)  # التاريخ
        header.setSectionResizeMode(3, QHeaderView.Fixed)  # النوع
        header.setSectionResizeMode(4, QHeaderView.Fixed)  # الحالة

        # تحديد عرض الأعمدة الثابتة
        self.notifications_table.setColumnWidth(0, 80)   # الرقم
        self.notifications_table.setColumnWidth(2, 150)  # التاريخ
        self.notifications_table.setColumnWidth(3, 150)  # النوع
        self.notifications_table.setColumnWidth(4, 120)  # الحالة

        self.notifications_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.notifications_table.setSelectionMode(QTableWidget.SingleSelection)
        self.notifications_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.notifications_table.setAlternatingRowColors(True)
        self.notifications_table.doubleClicked.connect(self.view_notification)

        # تحسين تصميم الجدول مع إطار أسود
        self.notifications_table.setStyleSheet("""
            QTableWidget {
                background-color: #ffffff;
                border: 3px solid #000000;
                border-radius: 8px;
                gridline-color: #e5e7eb;
                font-size: 13px;
                font-weight: bold;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e5e7eb;
                color: #000000;
                font-weight: bold;
            }
            QTableWidget::item:selected {
                background-color: #3b82f6;
                color: white;
            }
            QTableWidget::item:hover {
                background-color: #f3f4f6;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4f46e5, stop:1 #3730a3);
                color: white;
                padding: 8px;
                border: 2px solid #000000;
                font-weight: bold;
                font-size: 14px;
            }
        """)

    def refresh_data(self):
        """تحديث بيانات الإشعارات في الجدول"""
        # الحصول على جميع الإشعارات من قاعدة البيانات
        notifications = self.session.query(Notification).order_by(Notification.date.desc()).all()
        self.populate_table(notifications)
        self.update_summary(notifications)

    def populate_table(self, notifications):
        """ملء جدول الإشعارات بالبيانات"""
        self.notifications_table.setRowCount(0)

        for row, notification in enumerate(notifications):
            self.notifications_table.insertRow(row)
            self.notifications_table.setItem(row, 0, QTableWidgetItem(str(notification.id)))
            self.notifications_table.setItem(row, 1, QTableWidgetItem(notification.title))

            date = format_datetime(notification.date) if notification.date else ""
            self.notifications_table.setItem(row, 2, QTableWidgetItem(date))

            # ترجمة نوع الإشعار
            type_map = {
                'invoice_due': 'فاتورة مستحقة',
                'reminder_due': 'تنبيه مستحق'
            }
            type_text = type_map.get(notification.type, notification.type or "")
            self.notifications_table.setItem(row, 3, QTableWidgetItem(type_text))

            # حالة الإشعار (مقروء/غير مقروء)
            status_item = QTableWidgetItem("مقروء" if notification.is_read else "غير مقروء")

            # تلوين الإشعارات غير المقروءة باللون الأحمر
            if not notification.is_read:
                status_item.setForeground(QColor(255, 0, 0))
                # تلوين الصف بلون خفيف للإشارة إلى أنه غير مقروء
                for col in range(self.notifications_table.columnCount()):
                    item = self.notifications_table.item(row, col)
                    if item:
                        item.setBackground(QColor(255, 235, 235))

            self.notifications_table.setItem(row, 4, status_item)

    def update_summary(self, notifications):
        """تحديث ملخص الإشعارات"""
        total = len(notifications)
        unread = sum(1 for notification in notifications if not notification.is_read)

        self.total_label.setText(f"إجمالي الإشعارات: {total} | غير المقروءة: {unread}")

    def filter_notifications(self):
        """تصفية الإشعارات بناءً على نص البحث والنوع والحالة"""
        search_text = self.search_edit.text().strip().lower()
        notification_type = self.type_filter.currentData()
        is_read = self.read_filter.currentData()

        # بناء الاستعلام
        query = self.session.query(Notification)

        # تطبيق تصفية النص
        if search_text:
            query = query.filter(
                Notification.title.like(f"%{search_text}%") |
                Notification.message.like(f"%{search_text}%")
            )

        # تطبيق تصفية النوع
        if notification_type:
            query = query.filter(Notification.type == notification_type)

        # تطبيق تصفية الحالة
        if is_read is not None:
            query = query.filter(Notification.is_read == is_read)

        # تنفيذ الاستعلام
        notifications = query.order_by(Notification.date.desc()).all()

        # تحديث الجدول والملخص
        self.populate_table(notifications)
        self.update_summary(notifications)

    def view_notification(self):
        """عرض تفاصيل الإشعار"""
        selected_row = self.notifications_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار إشعار من القائمة")
            return

        notification_id = int(self.notifications_table.item(selected_row, 0).text())
        notification = self.session.query(Notification).get(notification_id)

        if not notification:
            show_error_message("خطأ", "لم يتم العثور على الإشعار")
            return

        # تعليم الإشعار كمقروء
        if not notification.is_read:
            notification.is_read = True
            self.session.commit()

        # إنشاء نافذة لعرض تفاصيل الإشعار
        dialog = QDialog(self)
        dialog.setWindowTitle(notification.title)
        dialog.setMinimumSize(500, 300)

        layout = QVBoxLayout()

        # عنوان الإشعار
        title_label = QLabel(notification.title)
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(title_label)

        # تاريخ الإشعار
        date_str = format_datetime(notification.date) if notification.date else ""
        date_label = QLabel(f"التاريخ: {date_str}")
        layout.addWidget(date_label)

        # نص الإشعار
        message_text = QTextEdit()
        message_text.setReadOnly(True)
        message_text.setText(notification.message)
        layout.addWidget(message_text)

        # إذا كان الإشعار متعلق بفاتورة، عرض معلومات الفاتورة
        if notification.type == 'invoice_due' and notification.related_id:
            invoice = self.session.query(Invoice).get(notification.related_id)
            if invoice:
                invoice_info = QLabel(f"الفاتورة: {invoice.invoice_number}")
                layout.addWidget(invoice_info)

                client_name = invoice.client.name if invoice.client else "غير محدد"
                client_info = QLabel(f"العميل: {client_name}")
                layout.addWidget(client_info)

                amount_info = QLabel(f"المبلغ المستحق: {invoice.total_amount - invoice.paid_amount:.2f}")
                layout.addWidget(amount_info)

                # زر للانتقال إلى الفاتورة
                goto_invoice_button = StyledButton("الانتقال إلى الفاتورة", "primary", "normal")
                goto_invoice_button.clicked.connect(lambda: self.goto_invoice(invoice.id))
                layout.addWidget(goto_invoice_button.button)

        # إذا كان الإشعار متعلق بتنبيه، عرض معلومات التنبيه
        elif notification.type == 'reminder_due' and notification.related_id:
            reminder = self.session.query(Reminder).get(notification.related_id)
            if reminder:
                reminder_title = QLabel(f"<b>عنوان التنبيه:</b> {reminder.title}")
                layout.addWidget(reminder_title)

                # تاريخ التنبيه
                date_str = format_datetime(reminder.reminder_date) if reminder.reminder_date else ""
                date_label = QLabel(f"<b>تاريخ التنبيه:</b> {date_str}")
                layout.addWidget(date_label)

                # الأولوية
                priority_map = {
                    'high': 'عالية',
                    'medium': 'متوسطة',
                    'low': 'منخفضة'
                }
                priority_text = priority_map.get(reminder.priority, reminder.priority or "")
                priority_label = QLabel(f"<b>الأولوية:</b> {priority_text}")
                layout.addWidget(priority_label)

                # الحالة
                status_text = "مكتمل" if reminder.is_completed else "غير مكتمل"
                status_label = QLabel(f"<b>الحالة:</b> {status_text}")
                layout.addWidget(status_label)

                # الوصف
                if reminder.description:
                    description_label = QLabel("<b>الوصف:</b>")
                    layout.addWidget(description_label)

                    description_text = QTextEdit()
                    description_text.setReadOnly(True)
                    description_text.setText(reminder.description)
                    description_text.setMaximumHeight(100)
                    layout.addWidget(description_text)

                # زر للانتقال إلى التنبيه
                goto_reminder_button = StyledButton("الانتقال إلى التنبيهات", "primary", "normal")
                goto_reminder_button.clicked.connect(lambda: self.goto_reminder(reminder.id))
                layout.addWidget(goto_reminder_button.button)

        # زر إغلاق
        close_button = StyledButton("إغلاق", "secondary", "normal")
        close_button.clicked.connect(dialog.accept)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(close_button.button)

        layout.addLayout(button_layout)
        dialog.setLayout(layout)

        dialog.exec_()

        # تحديث الجدول بعد عرض الإشعار
        self.refresh_data()

    def goto_invoice(self, invoice_id):
        """الانتقال إلى الفاتورة المرتبطة بالإشعار"""
        # هذه الوظيفة يجب أن تكون مرتبطة بواجهة الفواتير
        # يمكن تنفيذها لاحقًا عند ربط جميع الواجهات معًا
        show_info_message("معلومات", f"سيتم الانتقال إلى الفاتورة رقم {invoice_id}")

    def goto_reminder(self, reminder_id):
        """الانتقال إلى التنبيه المرتبط بالإشعار"""
        # التبديل إلى تبويب التنبيهات
        if hasattr(self, 'tabs') and hasattr(self, 'reminders_widget'):
            self.tabs.setCurrentWidget(self.reminders_widget)
            # تحديد التنبيه في قائمة التنبيهات إذا كان ذلك ممكنًا
            if hasattr(self.reminders_widget, 'select_reminder'):
                self.reminders_widget.select_reminder(reminder_id)
            else:
                show_info_message("معلومات", f"تم الانتقال إلى قسم التنبيهات، ولكن لا يمكن تحديد التنبيه رقم {reminder_id}")
        else:
            show_info_message("معلومات", f"لا يمكن الانتقال إلى التنبيه رقم {reminder_id}")

    def mark_as_read(self):
        """تعليم الإشعار كمقروء"""
        selected_row = self.notifications_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار إشعار من القائمة")
            return

        notification_id = int(self.notifications_table.item(selected_row, 0).text())
        notification = self.session.query(Notification).get(notification_id)

        if not notification:
            show_error_message("خطأ", "لم يتم العثور على الإشعار")
            return

        if notification.is_read:
            show_info_message("معلومات", "الإشعار مقروء بالفعل")
            return

        notification.is_read = True
        self.session.commit()
        show_info_message("تم", "تم تعليم الإشعار كمقروء")
        self.refresh_data()

    def mark_as_unread(self):
        """تعليم الإشعار كغير مقروء"""
        selected_row = self.notifications_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار إشعار من القائمة")
            return

        notification_id = int(self.notifications_table.item(selected_row, 0).text())
        notification = self.session.query(Notification).get(notification_id)

        if not notification:
            show_error_message("خطأ", "لم يتم العثور على الإشعار")
            return

        if not notification.is_read:
            show_info_message("معلومات", "الإشعار غير مقروء بالفعل")
            return

        notification.is_read = False
        self.session.commit()
        show_info_message("تم", "تم تعليم الإشعار كغير مقروء")
        self.refresh_data()

    def delete_notification(self):
        """حذف إشعار"""
        selected_row = self.notifications_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار إشعار من القائمة")
            return

        notification_id = int(self.notifications_table.item(selected_row, 0).text())
        notification = self.session.query(Notification).get(notification_id)

        if not notification:
            show_error_message("خطأ", "لم يتم العثور على الإشعار")
            return

        # طلب تأكيد الحذف
        if show_confirmation_message("تأكيد الحذف", f"هل أنت متأكد من حذف الإشعار '{notification.title}'؟"):
            self.session.delete(notification)
            self.session.commit()
            show_info_message("تم", "تم حذف الإشعار بنجاح")
            self.refresh_data()

    def check_due_invoices(self):
        """التحقق من الفواتير المستحقة وإنشاء إشعارات لها"""
        from utils import check_due_invoices

        due_invoices = check_due_invoices(self.session)

        if due_invoices:
            show_info_message("تم", f"تم العثور على {len(due_invoices)} فاتورة مستحقة وإنشاء إشعارات لها")
        else:
            show_info_message("معلومات", "لا توجد فواتير مستحقة جديدة")

        self.refresh_data()

    def check_due_reminders(self):
        """التحقق من التنبيهات المستحقة وإنشاء إشعارات لها"""
        from utils import check_due_reminders

        due_reminders = check_due_reminders(self.session)

        if due_reminders:
            show_info_message("تم", f"تم العثور على {len(due_reminders)} تنبيه مستحق وإنشاء إشعارات لها")
        else:
            show_info_message("معلومات", "لا توجد تنبيهات مستحقة جديدة")

        self.refresh_data()

    def show_statistics(self):
        """عرض إحصائيات الإشعارات"""
        show_info_message("إحصائيات الإشعارات", "سيتم إضافة ميزة الإحصائيات قريباً")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر
            colors = {
                'primary': {
                    'bg_start': '#1e3a8a', 'bg_mid': '#3b82f6', 'bg_end': '#1d4ed8', 'bg_bottom': '#1e40af',
                    'hover_start': '#3b82f6', 'hover_mid': '#60a5fa', 'hover_end': '#2563eb', 'hover_bottom': '#1d4ed8',
                    'hover_border': '#2563eb', 'pressed_start': '#1e40af', 'pressed_mid': '#1d4ed8',
                    'pressed_end': '#1e3a8a', 'pressed_bottom': '#172554', 'pressed_border': '#1e3a8a',
                    'border': '#1d4ed8', 'text': '#ffffff'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#10b981', 'bg_end': '#059669', 'bg_bottom': '#022c22',
                    'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#059669', 'hover_bottom': '#064e3b',
                    'hover_border': '#059669', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#014737', 'pressed_bottom': '#014737', 'pressed_border': '#064e3b',
                    'border': '#059669', 'text': '#ffffff'
                },
                'danger': {
                    'bg_start': '#991b1b', 'bg_mid': '#ef4444', 'bg_end': '#dc2626', 'bg_bottom': '#7f1d1d',
                    'hover_start': '#ef4444', 'hover_mid': '#f87171', 'hover_end': '#dc2626', 'hover_bottom': '#991b1b',
                    'hover_border': '#dc2626', 'pressed_start': '#7f1d1d', 'pressed_mid': '#991b1b',
                    'pressed_end': '#450a0a', 'pressed_bottom': '#450a0a', 'pressed_border': '#991b1b',
                    'border': '#dc2626', 'text': '#ffffff'
                },
                'info': {
                    'bg_start': '#0891b2', 'bg_mid': '#0ea5e9', 'bg_end': '#0284c7', 'bg_bottom': '#075985',
                    'hover_start': '#0ea5e9', 'hover_mid': '#38bdf8', 'hover_end': '#0891b2', 'hover_bottom': '#0284c7',
                    'hover_border': '#0891b2', 'pressed_start': '#0284c7', 'pressed_mid': '#0891b2',
                    'pressed_end': '#075985', 'pressed_bottom': '#0c4a6e', 'pressed_border': '#075985',
                    'border': '#0891b2', 'text': '#ffffff'
                },
                'modern_teal': {
                    'bg_start': '#0d9488', 'bg_mid': '#14b8a6', 'bg_end': '#0f766e', 'bg_bottom': '#134e4a',
                    'hover_start': '#14b8a6', 'hover_mid': '#2dd4bf', 'hover_end': '#0d9488', 'hover_bottom': '#0f766e',
                    'hover_border': '#14b8a6', 'pressed_start': '#0f766e', 'pressed_mid': '#0d9488',
                    'pressed_end': '#134e4a', 'pressed_bottom': '#042f2e', 'pressed_border': '#0f766e',
                    'border': '#0d9488', 'text': '#ffffff'
                },
                'cyan': {
                    'bg_start': '#0891b2', 'bg_mid': '#06b6d4', 'bg_end': '#0e7490', 'bg_bottom': '#164e63',
                    'hover_start': '#06b6d4', 'hover_mid': '#22d3ee', 'hover_end': '#0891b2', 'hover_bottom': '#0e7490',
                    'hover_border': '#06b6d4', 'pressed_start': '#0e7490', 'pressed_mid': '#0891b2',
                    'pressed_end': '#164e63', 'pressed_bottom': '#083344', 'pressed_border': '#0e7490',
                    'border': '#0891b2', 'text': '#ffffff'
                },
                'rose': {
                    'bg_start': '#be185d', 'bg_mid': '#ec4899', 'bg_end': '#be185d', 'bg_bottom': '#9d174d',
                    'hover_start': '#ec4899', 'hover_mid': '#f472b6', 'hover_end': '#be185d', 'hover_bottom': '#be185d',
                    'hover_border': '#ec4899', 'pressed_start': '#9d174d', 'pressed_mid': '#be185d',
                    'pressed_end': '#831843', 'pressed_bottom': '#500724', 'pressed_border': '#9d174d',
                    'border': '#be185d', 'text': '#ffffff'
                },
                'indigo': {
                    'bg_start': '#3730a3', 'bg_mid': '#6366f1', 'bg_end': '#4f46e5', 'bg_bottom': '#312e81',
                    'hover_start': '#6366f1', 'hover_mid': '#818cf8', 'hover_end': '#4f46e5', 'hover_bottom': '#3730a3',
                    'hover_border': '#6366f1', 'pressed_start': '#312e81', 'pressed_mid': '#3730a3',
                    'pressed_end': '#1e1b4b', 'pressed_bottom': '#1e1b4b', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff'
                },
                'orange': {
                    'bg_start': '#c2410c', 'bg_mid': '#f97316', 'bg_end': '#ea580c', 'bg_bottom': '#9a3412',
                    'hover_start': '#f97316', 'hover_mid': '#fb923c', 'hover_end': '#ea580c', 'hover_bottom': '#c2410c',
                    'hover_border': '#f97316', 'pressed_start': '#9a3412', 'pressed_mid': '#c2410c',
                    'pressed_end': '#7c2d12', 'pressed_bottom': '#431407', 'pressed_border': '#9a3412',
                    'border': '#ea580c', 'text': '#ffffff'
                }
            }

            # الحصول على ألوان الزر المحدد
            color_scheme = colors.get(button_type, colors['primary'])

            # تحديد نمط القائمة المنسدلة إذا كان الزر يحتوي على قائمة
            menu_indicator = "::menu-indicator { width: 0px; }" if not has_menu else ""

            # تطبيق التصميم المتطور
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.3 {color_scheme['bg_mid']},
                        stop:0.7 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 2px solid {color_scheme['border']};
                    border-radius: 8px;
                    padding: 8px 16px;
                    font-weight: bold;
                    font-size: 13px;
                    min-height: 38px;
                    max-height: 38px;
                    min-width: 100px;
                    text-align: center;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.3 {color_scheme['hover_mid']},
                        stop:0.7 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 2px solid {color_scheme['hover_border']};
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.3 {color_scheme['pressed_mid']},
                        stop:0.7 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 2px solid {color_scheme['pressed_border']};
                }}
                {menu_indicator}
            """

            button.setStyleSheet(style)
            print(f"✅ تم تطبيق التصميم المتطور على الزر: {button.text()} - نوع: {button_type}")

        except Exception as e:
            print(f"❌ خطأ في تطبيق التصميم على الزر: {str(e)}")

    def style_compact_button(self, button, button_type):
        """تطبيق تصميم مدمج ومحسن على الأزرار"""
        try:
            # تحديد الألوان المحسنة للأزرار المدمجة
            colors = {
                'primary': {
                    'bg': '#3b82f6', 'hover': '#2563eb', 'pressed': '#1d4ed8',
                    'border': '#1d4ed8', 'text': '#ffffff'
                },
                'emerald': {
                    'bg': '#10b981', 'hover': '#059669', 'pressed': '#047857',
                    'border': '#059669', 'text': '#ffffff'
                },
                'danger': {
                    'bg': '#ef4444', 'hover': '#dc2626', 'pressed': '#b91c1c',
                    'border': '#dc2626', 'text': '#ffffff'
                },
                'info': {
                    'bg': '#0ea5e9', 'hover': '#0284c7', 'pressed': '#0369a1',
                    'border': '#0284c7', 'text': '#ffffff'
                },
                'modern_teal': {
                    'bg': '#14b8a6', 'hover': '#0d9488', 'pressed': '#0f766e',
                    'border': '#0d9488', 'text': '#ffffff'
                },
                'cyan': {
                    'bg': '#06b6d4', 'hover': '#0891b2', 'pressed': '#0e7490',
                    'border': '#0891b2', 'text': '#ffffff'
                },
                'rose': {
                    'bg': '#ec4899', 'hover': '#be185d', 'pressed': '#9d174d',
                    'border': '#be185d', 'text': '#ffffff'
                },
                'indigo': {
                    'bg': '#6366f1', 'hover': '#4f46e5', 'pressed': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff'
                },
                'orange': {
                    'bg': '#f97316', 'hover': '#ea580c', 'pressed': '#c2410c',
                    'border': '#ea580c', 'text': '#ffffff'
                }
            }

            # الحصول على ألوان الزر المحدد
            color_scheme = colors.get(button_type, colors['primary'])

            # تطبيق التصميم المدمج
            style = f"""
                QPushButton {{
                    background-color: {color_scheme['bg']};
                    color: {color_scheme['text']};
                    border: 2px solid {color_scheme['border']};
                    border-radius: 6px;
                    padding: 6px 12px;
                    font-weight: bold;
                    font-size: 11px;
                    min-height: 24px;
                    max-height: 28px;
                    text-align: center;
                }}
                QPushButton:hover {{
                    background-color: {color_scheme['hover']};
                    border: 2px solid {color_scheme['hover']};
                }}
                QPushButton:pressed {{
                    background-color: {color_scheme['pressed']};
                    border: 2px solid {color_scheme['pressed']};
                }}
            """

            button.setStyleSheet(style)
            print(f"✅ تم تطبيق التصميم المدمج على الزر: {button.text()} - نوع: {button_type}")

        except Exception as e:
            print(f"❌ خطأ في تطبيق التصميم المدمج على الزر: {str(e)}")

