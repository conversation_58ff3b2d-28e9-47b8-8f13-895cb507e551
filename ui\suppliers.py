from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                            QMenu, QAction, QFrame, QSizePolicy)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QColor

from datetime import datetime

from ui.unified_styles import (UnifiedStyles, StyledButton,
                                StyledTable, StyledLabel, BaseDialog)
from database import Supplier, SupplierPhone, Expense, update_supplier_balance
from utils import show_error_message, show_info_message, show_confirmation_message, is_valid_email, is_valid_phone, format_currency, format_date

class SuppliersWidget(QWidget):
    """واجهة إدارة الموردين"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.init_ui()
        self.refresh_data()



    def init_ui(self):
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(8)

        # إضافة العنوان الرئيسي المطور والمحسن
        title_label = QLabel("🚛 إدارة الموردين المتطورة - نظام شامل ومتقدم لإدارة بيانات الموردين مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e40af,
                    stop:0.2 #3b82f6,
                    stop:0.4 #6366f1,
                    stop:0.6 #8b5cf6,
                    stop:0.8 #a855f7,
                    stop:1 #c084fc);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;

            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء إطار علوي محسن بنفس الأسلوب القديم (صف واحد)
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)

        # تخطيط أفقي واحد محسن (الطريقة القديمة)
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        search_layout.setSpacing(4)  # مسافات متوازنة

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch(1)

        # تسمية البحث محسنة مع ألوان موحدة
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 14px;
                font-weight: bold;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #dc2626,
                    stop:0.5 #b91c1c,
                    stop:1 #991b1b);
                border: 3px solid #7f1d1d;
                border-radius: 12px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ef4444,
                    stop:0.5 #dc2626,
                    stop:1 #b91c1c);
                border: 3px solid #ef4444;
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث بالاسم، الهاتف، الإيميل أو العنوان...")
        self.search_edit.textChanged.connect(self.filter_suppliers)
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff,
                    stop:0.5 #f8fafc,
                    stop:1 #e2e8f0);
                border: 3px solid #4f46e5;
                border-radius: 12px;
                padding: 8px 15px;
                font-size: 14px;
                font-weight: bold;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: #4f46e5;
            }
            QLineEdit:focus {
                border: 3px solid #3730a3;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f0f9ff,
                    stop:1 #e0f2fe);
            }
            QLineEdit:hover {
                border: 3px solid #5b52f0;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #fafbff,
                    stop:1 #f1f5f9);
            }
        """)

        search_button = QPushButton("🔍")
        search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #0891b2,
                    stop:0.5 #0e7490,
                    stop:1 #155e75);
                color: #ffffff;
                border: 3px solid #164e63;
                border-radius: 12px;
                padding: 8px;
                font-size: 20px;
                font-weight: bold;
                min-width: 50px;
                max-width: 50px;
                max-height: 38px;
                min-height: 34px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #22d3ee,
                    stop:0.5 #0891b2,
                    stop:1 #0e7490);
                border: 3px solid #22d3ee;
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #0e7490,
                    stop:1 #155e75);
                border: 3px solid #0e7490;
            }
        """)
        search_button.clicked.connect(self.filter_suppliers)
        search_button.setToolTip("بحث متقدم")
        search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        search_button.setContentsMargins(0, 0, 0, 0)

        # تسمية التصفية مطورة بألوان احترافية
        filter_label = QLabel("🎯 تصفية:")
        filter_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 14px;
                font-weight: bold;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f59e0b,
                    stop:0.5 #d97706,
                    stop:1 #b45309);
                border: 3px solid #92400e;
                border-radius: 12px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #fbbf24,
                    stop:0.5 #f59e0b,
                    stop:1 #d97706);
                border: 3px solid #fbbf24;
            }
        """)
        filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        # أزرار التصفية السريعة بألوان مختلفة ومميزة
        filter_all_button = QPushButton("📊 الكل")
        self.style_advanced_button(filter_all_button, 'slate')  # رمادي داكن للكل
        filter_all_button.clicked.connect(lambda: self.filter_by_balance('all'))
        filter_all_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        filter_positive_button = QPushButton("💰 لهم مبلغ")
        self.style_advanced_button(filter_positive_button, 'emerald')  # أخضر زمردي مختلف
        filter_positive_button.clicked.connect(lambda: self.filter_by_balance('positive'))
        filter_positive_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        filter_negative_button = QPushButton("⚠️ عليهم مبلغ")
        self.style_advanced_button(filter_negative_button, 'danger')  # أحمر للسالب
        filter_negative_button.clicked.connect(lambda: self.filter_by_balance('negative'))
        filter_negative_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        filter_zero_button = QPushButton("⚪ بدون رصيد")
        self.style_advanced_button(filter_zero_button, 'secondary')  # رمادي فاتح مختلف
        filter_zero_button.clicked.connect(lambda: self.filter_by_balance('zero'))
        filter_zero_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        # أزرار الأدوات المتقدمة بنفس التنسيق
        columns_button = QPushButton("👁️ الأعمدة")
        self.style_advanced_button(columns_button, 'purple')  # بنفسجي للأعمدة
        columns_button.clicked.connect(self.show_columns_dialog)
        columns_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        bulk_operations_button = QPushButton("📋 عمليات مجمعة")
        self.style_advanced_button(bulk_operations_button, 'indigo')  # نيلي للعمليات
        bulk_operations_button.clicked.connect(self.show_bulk_operations_dialog)
        bulk_operations_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        advanced_filters_button = QPushButton("🎯 فلاتر متقدمة")
        self.style_advanced_button(advanced_filters_button, 'rose')  # وردي للفلاتر
        advanced_filters_button.clicked.connect(self.show_advanced_filters_dialog)
        advanced_filters_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        # تطبيق نفس مقاسات الإطار السفلي على أزرار الإطار العلوي
        top_buttons = [filter_all_button, filter_positive_button, filter_negative_button,
                      filter_zero_button, columns_button, bulk_operations_button, advanced_filters_button]

        for button in top_buttons:
            button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)  # تغيير سياسة الحجم
            button.setMinimumWidth(95)  # نفس العرض الأدنى للإطار السفلي
            button.setMaximumHeight(38)  # ارتفاع أكبر ليناسب الإطار الأكبر (75px)
            button.setMinimumHeight(34)  # ارتفاع أدنى أكبر
            button.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش الإضافية

            # حجم خط مناسب للارتفاع الجديد
            font = button.font()
            font.setPointSize(12)  # حجم خط أكبر ليناسب الارتفاع الأكبر
            font.setBold(True)
            button.setFont(font)

        # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter)  # يأخذ مساحة أكبر
        search_layout.addWidget(search_button, 0, Qt.AlignVCenter)
        search_layout.addWidget(filter_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(filter_all_button, 1, Qt.AlignVCenter)  # توسيع الأزرار
        search_layout.addWidget(filter_positive_button, 1, Qt.AlignVCenter)
        search_layout.addWidget(filter_negative_button, 1, Qt.AlignVCenter)
        search_layout.addWidget(filter_zero_button, 1, Qt.AlignVCenter)
        search_layout.addWidget(columns_button, 1, Qt.AlignVCenter)
        search_layout.addWidget(bulk_operations_button, 1, Qt.AlignVCenter)
        search_layout.addWidget(advanced_filters_button, 1, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        top_frame.setLayout(top_container)

        # إنشاء جدول الموردين المتطور والمحسن
        self.create_advanced_suppliers_table()

        main_layout.addWidget(top_frame)
        main_layout.addWidget(self.suppliers_table, 1)  # إعطاء الجدول أولوية في التمدد

        # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        actions_layout.setSpacing(4)  # مسافة أكبر بين الأزرار لتوزيع أفضل

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # إنشاء الأزرار بأحجام متناسقة حسب الترتيب الجديد

        # المجموعة الأولى - العمليات الأساسية مع ألوان متنوعة
        self.add_button = QPushButton("➕ إضافة مورد ▼")
        self.style_advanced_button(self.add_button, 'emerald', has_menu=True)  # أخضر زمردي مميز مع قائمة
        self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة منسدلة لإضافة المورد مع النمط الموحد الجديد
        add_menu = QMenu(self)
        from ui.unified_styles import UnifiedStyles
        add_menu.setStyleSheet(UnifiedStyles.get_menu_style('success', 'normal'))

        # إضافة خيارات الإضافة المتقدمة
        add_normal_action = QAction("🏪 إضافة مورد عادي", self)
        add_normal_action.triggered.connect(self.add_supplier)
        add_menu.addAction(add_normal_action)

        add_menu.addSeparator()

        quick_add_action = QAction("⚡ إضافة سريعة", self)
        quick_add_action.triggered.connect(self.quick_add_supplier)
        add_menu.addAction(quick_add_action)

        import_action = QAction("📥 استيراد من ملف", self)
        import_action.triggered.connect(self.import_suppliers)
        add_menu.addAction(import_action)

        # ربط القائمة بالزر
        self.add_button.setMenu(add_menu)

        self.edit_button = QPushButton("✏️ تعديل")
        self.style_advanced_button(self.edit_button, 'primary')  # أزرق كلاسيكي
        self.edit_button.clicked.connect(self.edit_supplier)
        self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.delete_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_button, 'danger')  # أحمر تحذيري
        self.delete_button.clicked.connect(self.delete_supplier)
        self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_button, 'modern_teal')  # تصميم حديث ومتطور
        self.refresh_button.clicked.connect(self.modern_refresh_data)
        self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # المجموعة الثانية - العمليات المالية والملفات مع ألوان متنوعة
        self.balance_button = QPushButton("💰 تعديل الرصيد")
        self.style_advanced_button(self.balance_button, 'orange')  # برتقالي مالي
        self.balance_button.clicked.connect(self.adjust_balance)
        self.balance_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.attachments_button = QPushButton("📎 إضافة المرفقات")
        self.style_advanced_button(self.attachments_button, 'purple')  # بنفسجي مميز
        self.attachments_button.clicked.connect(self.manage_attachments)
        self.attachments_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.details_button = QPushButton("👁️ عرض التفاصيل ▼")
        self.style_advanced_button(self.details_button, 'indigo', has_menu=True)  # نيلي عميق
        self.details_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.call_button = QPushButton("📞 اتصال سريع ▼")
        self.style_advanced_button(self.call_button, 'lime', has_menu=True)  # أخضر ليموني مختلف للاتصال
        self.call_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة منسدلة لعرض التفاصيل مع النمط الموحد الجديد
        details_menu = QMenu(self)
        details_menu.setStyleSheet(UnifiedStyles.get_menu_style('primary', 'normal'))

        # إضافة خيارات عرض التفاصيل
        basic_details_action = QAction("🏪 التفاصيل الأساسية", self)
        basic_details_action.triggered.connect(self.show_supplier_details)
        details_menu.addAction(basic_details_action)

        financial_details_action = QAction("💰 التفاصيل المالية", self)
        financial_details_action.triggered.connect(self.show_financial_details)
        details_menu.addAction(financial_details_action)

        contact_details_action = QAction("📞 تفاصيل الاتصال", self)
        contact_details_action.triggered.connect(self.show_contact_details)
        details_menu.addAction(contact_details_action)

        history_details_action = QAction("📋 سجل المعاملات", self)
        history_details_action.triggered.connect(self.show_transaction_history)
        details_menu.addAction(history_details_action)

        # ربط القائمة بالزر
        self.details_button.setMenu(details_menu)

        self.export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.export_button, 'info', has_menu=True)  # لون متسق مع نظام الألوان
        self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة منسدلة للتصدير مع النمط الموحد الجديد
        export_menu = QMenu(self)
        export_menu.setStyleSheet(UnifiedStyles.get_menu_style('warning', 'normal'))

        # إضافة خيارات التصدير
        export_excel_action = QAction("📊 تصدير إلى Excel", self)
        export_excel_action.triggered.connect(self.export_data)
        export_menu.addAction(export_excel_action)

        export_pdf_action = QAction("📄 تصدير إلى PDF", self)
        export_pdf_action.triggered.connect(self.export_to_pdf)
        export_menu.addAction(export_pdf_action)

        export_csv_action = QAction("📋 تصدير إلى CSV", self)
        export_csv_action.triggered.connect(self.export_to_csv)
        export_menu.addAction(export_csv_action)

        export_json_action = QAction("🔗 تصدير إلى JSON", self)
        export_json_action.triggered.connect(self.export_to_json)
        export_menu.addAction(export_json_action)

        # ربط القائمة بالزر
        self.export_button.setMenu(export_menu)

        # المجموعة الثالثة - التقارير والإحصائيات مع ألوان متنوعة
        self.report_button = QPushButton("📋 التقارير")
        self.style_advanced_button(self.report_button, 'cyan')  # سيان مميز للتقارير
        self.report_button.clicked.connect(self.generate_suppliers_report)
        self.report_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.statistics_button = QPushButton("📊 الإحصائيات ▼")
        self.style_advanced_button(self.statistics_button, 'rose', has_menu=True)  # وردي للإحصائيات
        self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # المجموعة الرابعة - النظام مع ألوان متنوعة
        self.backup_button = QPushButton("💾 نسخ احتياطي")
        self.style_advanced_button(self.backup_button, 'warning')  # كهرماني للنسخ الاحتياطي
        self.backup_button.clicked.connect(self.backup_suppliers_data)
        self.backup_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.restore_button = QPushButton("📥 استعادة")
        self.style_advanced_button(self.restore_button, 'slate')  # رمادي داكن مميز للاستعادة
        self.restore_button.clicked.connect(self.restore_suppliers_data)
        self.restore_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة منسدلة للاتصال السريع مع النمط الموحد الجديد
        call_menu = QMenu(self)
        call_menu.setStyleSheet(UnifiedStyles.get_menu_style('success', 'normal'))

        # إضافة خيارات الاتصال المتقدمة
        direct_call_action = QAction("📞 اتصال مباشر", self)
        direct_call_action.triggered.connect(self.make_direct_call)
        call_menu.addAction(direct_call_action)

        whatsapp_call_action = QAction("💬 اتصال واتساب", self)
        whatsapp_call_action.triggered.connect(self.make_whatsapp_call)
        call_menu.addAction(whatsapp_call_action)

        call_menu.addSeparator()

        copy_number_action = QAction("📋 نسخ الرقم", self)
        copy_number_action.triggered.connect(self.copy_phone_number)
        call_menu.addAction(copy_number_action)

        save_contact_action = QAction("👤 حفظ في جهات الاتصال", self)
        save_contact_action.triggered.connect(self.save_to_contacts)
        call_menu.addAction(save_contact_action)

        call_history_action = QAction("📋 سجل المكالمات", self)
        call_history_action.triggered.connect(self.show_call_history)
        call_menu.addAction(call_history_action)

        # ربط القائمة بالزر
        self.call_button.setMenu(call_menu)

        # إنشاء قائمة منسدلة للإحصائيات مع النمط الموحد الجديد
        statistics_menu = QMenu(self)
        statistics_menu.setStyleSheet(UnifiedStyles.get_menu_style('danger', 'normal'))

        # إضافة خيارات الإحصائيات
        basic_stats_action = QAction("📈 إحصائيات أساسية", self)
        basic_stats_action.triggered.connect(self.show_statistics)
        statistics_menu.addAction(basic_stats_action)

        detailed_stats_action = QAction("📊 إحصائيات مفصلة", self)
        detailed_stats_action.triggered.connect(self.show_detailed_statistics)
        statistics_menu.addAction(detailed_stats_action)

        balance_analysis_action = QAction("💰 تحليل الأرصدة", self)
        balance_analysis_action.triggered.connect(self.show_balance_analysis)
        statistics_menu.addAction(balance_analysis_action)

        monthly_report_action = QAction("📅 تقرير شهري", self)
        monthly_report_action.triggered.connect(self.show_monthly_report)
        statistics_menu.addAction(monthly_report_action)

        # ربط القائمة بالزر
        self.statistics_button.setMenu(statistics_menu)

        # إضافة الأزرار بالترتيب المطلوب مع ألوان متنوعة ومميزة

        # المجموعة الأولى - العمليات الأساسية مع التوسيط العمودي
        actions_layout.addWidget(self.add_button, 0, Qt.AlignVCenter)           # 1. ➕ إضافة مورد (أخضر)
        actions_layout.addWidget(self.edit_button, 0, Qt.AlignVCenter)          # 2. ✏️ تعديل (أزرق)
        actions_layout.addWidget(self.delete_button, 0, Qt.AlignVCenter)        # 3. 🗑️ حذف (أحمر)
        actions_layout.addWidget(self.refresh_button, 0, Qt.AlignVCenter)       # 4. 🔄 تحديث (تيل)

        # فاصل راقي 1
        actions_layout.addWidget(self.create_elegant_separator(), 0, Qt.AlignVCenter)

        # المجموعة الثانية - العمليات المالية والملفات مع التوسيط العمودي
        actions_layout.addWidget(self.balance_button, 0, Qt.AlignVCenter)       # 5. 💰 تعديل رصيد (برتقالي)
        actions_layout.addWidget(self.attachments_button, 0, Qt.AlignVCenter)   # 6. 📎 إضافة المرفقات (بنفسجي)
        actions_layout.addWidget(self.details_button, 0, Qt.AlignVCenter)       # 7. 👁️ عرض التفاصيل (نيلي)

        # فاصل راقي 2
        actions_layout.addWidget(self.create_elegant_separator(), 0, Qt.AlignVCenter)

        # المجموعة الثالثة - الاتصال مع التوسيط العمودي
        actions_layout.addWidget(self.call_button, 0, Qt.AlignVCenter)          # 8. 📞 اتصال سريع (زمردي)

        # فاصل راقي 3
        actions_layout.addWidget(self.create_elegant_separator(), 0, Qt.AlignVCenter)

        # المجموعة الرابعة - التقارير والإحصائيات مع التوسيط العمودي (ترتيب جديد)
        actions_layout.addWidget(self.statistics_button, 0, Qt.AlignVCenter)    # 9. 📊 الإحصائيات (وردي)
        actions_layout.addWidget(self.report_button, 0, Qt.AlignVCenter)        # 10. 📋 التقارير (سيان)

        # فاصل راقي 4
        actions_layout.addWidget(self.create_elegant_separator(), 0, Qt.AlignVCenter)

        # المجموعة الخامسة - النظام مع التوسيط العمودي (ترتيب محسن)
        actions_layout.addWidget(self.backup_button, 0, Qt.AlignVCenter)        # 11. 💾 نسخ احتياطي (كهرماني)
        actions_layout.addWidget(self.export_button, 0, Qt.AlignVCenter)        # 12. 📤 تصدير (تيل فاتح)
        actions_layout.addWidget(self.restore_button, 0, Qt.AlignVCenter)       # 13. 📥 استعادة (رمادي داكن)

        # تطبيق الأحجام الجديدة على جميع الأزرار بما في ذلك زر التصدير
        all_buttons = [
            self.add_button, self.edit_button, self.delete_button, self.refresh_button,
            self.balance_button, self.attachments_button, self.details_button, self.call_button,
            self.statistics_button, self.report_button, self.backup_button, self.export_button, self.restore_button
        ]

        for button in all_buttons:
            # تطبيق الأحجام الجديدة المناسبة للإطار الأكبر (75px)
            button.setMaximumHeight(38)  # ارتفاع أكبر ليناسب الإطار الأكبر
            button.setMinimumHeight(34)  # ارتفاع أدنى أكبر
            button.setMinimumWidth(95)   # عرض أدنى ثابت
            button.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش

            # تطبيق خط أكبر ليناسب الارتفاع الجديد
            font = button.font()
            font.setPointSize(12)  # حجم خط أكبر ليناسب الارتفاع الأكبر
            font.setBold(True)
            button.setFont(font)

        # تعيين التخطيط للإطار السفلي - استخدام الحاوي العمودي للتوسيط
        bottom_frame.setLayout(bottom_container)

        # تجميع التخطيط النهائي مع الإطارات واستغلال العرض الكامل
        main_layout.addWidget(bottom_frame)

        # ضمان استغلال العرض الكامل للتخطيط الرئيسي
        main_layout.setContentsMargins(5, 5, 5, 5)  # هوامش صغيرة
        main_layout.setSpacing(5)  # مسافات صغيرة بين العناصر

        self.setLayout(main_layout)

        # ضمان أن الواجهة تستغل العرض الكامل
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

    def create_elegant_separator(self):
        """إنشاء فاصل أسود يملأ ارتفاع الإطار من الأعلى للأسفل"""
        try:
            separator = QFrame()
            separator.setFixedWidth(2)  # عرض رفيع جداً
            separator.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Expanding)  # يتمدد عمودياً
            separator.setStyleSheet("""
                QFrame {
                    background-color: #000000;
                    border: none;
                    border-radius: 1px;
                    margin: 0px 4px;
                    padding: 0px;
                }
            """)
            return separator
        except Exception as e:
            print(f"❌ خطأ في إنشاء الفاصل الأسود: {str(e)}")
            return QFrame()

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر
            colors = {
                'primary': {
                    'bg_start': '#1e3a8a',
                    'bg_mid': '#3b82f6',
                    'bg_end': '#1d4ed8',
                    'bg_bottom': '#1e40af',
                    'hover_start': '#3b82f6',
                    'hover_mid': '#60a5fa',
                    'hover_end': '#2563eb',
                    'hover_bottom': '#1d4ed8',
                    'hover_border': '#2563eb',
                    'pressed_start': '#1e40af',
                    'pressed_mid': '#1d4ed8',
                    'pressed_end': '#1e3a8a',
                    'pressed_bottom': '#172554',
                    'pressed_border': '#1e3a8a',
                    'border': '#1d4ed8',
                    'text': '#ffffff'
                },
                'emerald': {
                    'bg_start': '#064e3b',
                    'bg_mid': '#10b981',
                    'bg_end': '#059669',
                    'bg_bottom': '#022c22',
                    'hover_start': '#10b981',
                    'hover_mid': '#34d399',
                    'hover_end': '#059669',
                    'hover_bottom': '#064e3b',
                    'hover_border': '#059669',
                    'pressed_start': '#022c22',
                    'pressed_mid': '#064e3b',
                    'pressed_end': '#014737',
                    'pressed_bottom': '#014737',
                    'pressed_border': '#064e3b',
                    'border': '#059669',
                    'text': '#ffffff'
                },
                'danger': {
                    'bg_start': '#991b1b',
                    'bg_mid': '#ef4444',
                    'bg_end': '#dc2626',
                    'bg_bottom': '#7f1d1d',
                    'hover_start': '#ef4444',
                    'hover_mid': '#f87171',
                    'hover_end': '#dc2626',
                    'hover_bottom': '#991b1b',
                    'hover_border': '#dc2626',
                    'pressed_start': '#7f1d1d',
                    'pressed_mid': '#991b1b',
                    'pressed_end': '#450a0a',
                    'pressed_bottom': '#450a0a',
                    'pressed_border': '#991b1b',
                    'border': '#dc2626',
                    'text': '#ffffff'
                },
                'warning': {
                    'bg_start': '#92400e',
                    'bg_mid': '#f59e0b',
                    'bg_end': '#d97706',
                    'bg_bottom': '#78350f',
                    'hover_start': '#f59e0b',
                    'hover_mid': '#fbbf24',
                    'hover_end': '#d97706',
                    'hover_bottom': '#92400e',
                    'hover_border': '#d97706',
                    'pressed_start': '#78350f',
                    'pressed_mid': '#92400e',
                    'pressed_end': '#451a03',
                    'pressed_bottom': '#451a03',
                    'pressed_border': '#92400e',
                    'border': '#d97706',
                    'text': '#ffffff'
                },
                'purple': {
                    'bg_start': '#581c87',
                    'bg_mid': '#a855f7',
                    'bg_end': '#9333ea',
                    'bg_bottom': '#4c1d95',
                    'hover_start': '#a855f7',
                    'hover_mid': '#c084fc',
                    'hover_end': '#9333ea',
                    'hover_bottom': '#581c87',
                    'hover_border': '#9333ea',
                    'pressed_start': '#4c1d95',
                    'pressed_mid': '#581c87',
                    'pressed_end': '#312e81',
                    'pressed_bottom': '#312e81',
                    'pressed_border': '#581c87',
                    'border': '#9333ea',
                    'text': '#ffffff'
                },
                'indigo': {
                    'bg_start': '#3730a3',
                    'bg_mid': '#6366f1',
                    'bg_end': '#4f46e5',
                    'bg_bottom': '#312e81',
                    'hover_start': '#6366f1',
                    'hover_mid': '#818cf8',
                    'hover_end': '#4f46e5',
                    'hover_bottom': '#3730a3',
                    'hover_border': '#4f46e5',
                    'pressed_start': '#312e81',
                    'pressed_mid': '#3730a3',
                    'pressed_end': '#1e1b4b',
                    'pressed_bottom': '#1e1b4b',
                    'pressed_border': '#3730a3',
                    'border': '#4f46e5',
                    'text': '#ffffff'
                },
                'modern_teal': {
                    'bg_start': '#0891b2',
                    'bg_mid': '#0ea5e9',
                    'bg_end': '#0284c7',
                    'bg_bottom': '#075985',
                    'hover_start': '#0ea5e9',
                    'hover_mid': '#38bdf8',
                    'hover_end': '#0891b2',
                    'hover_bottom': '#0284c7',
                    'hover_border': '#0891b2',
                    'pressed_start': '#0284c7',
                    'pressed_mid': '#0891b2',
                    'pressed_end': '#075985',
                    'pressed_bottom': '#0c4a6e',
                    'pressed_border': '#075985',
                    'border': '#0891b2',
                    'text': '#ffffff'
                },
                'rose': {
                    'bg_start': '#9f1239',
                    'bg_mid': '#f43f5e',
                    'bg_end': '#e11d48',
                    'bg_bottom': '#881337',
                    'hover_start': '#f43f5e',
                    'hover_mid': '#fb7185',
                    'hover_end': '#e11d48',
                    'hover_bottom': '#9f1239',
                    'hover_border': '#e11d48',
                    'pressed_start': '#881337',
                    'pressed_mid': '#9f1239',
                    'pressed_end': '#4c0519',
                    'pressed_bottom': '#4c0519',
                    'pressed_border': '#9f1239',
                    'border': '#e11d48',
                    'text': '#ffffff'
                },
                'cyan': {
                    'bg_start': '#155e75',
                    'bg_mid': '#06b6d4',
                    'bg_end': '#0891b2',
                    'bg_bottom': '#164e63',
                    'hover_start': '#06b6d4',
                    'hover_mid': '#67e8f9',
                    'hover_end': '#0891b2',
                    'hover_bottom': '#155e75',
                    'hover_border': '#0891b2',
                    'pressed_start': '#164e63',
                    'pressed_mid': '#155e75',
                    'pressed_end': '#083344',
                    'pressed_bottom': '#083344',
                    'pressed_border': '#155e75',
                    'border': '#0891b2',
                    'text': '#ffffff'
                },
                'orange': {
                    'bg_start': '#9a3412',
                    'bg_mid': '#ea580c',
                    'bg_end': '#dc2626',
                    'bg_bottom': '#7c2d12',
                    'hover_start': '#ea580c',
                    'hover_mid': '#fb923c',
                    'hover_end': '#dc2626',
                    'hover_bottom': '#9a3412',
                    'hover_border': '#dc2626',
                    'pressed_start': '#7c2d12',
                    'pressed_mid': '#9a3412',
                    'pressed_end': '#431407',
                    'pressed_bottom': '#431407',
                    'pressed_border': '#9a3412',
                    'border': '#dc2626',
                    'text': '#ffffff'
                },
                'slate': {
                    'bg_start': '#334155',
                    'bg_mid': '#64748b',
                    'bg_end': '#475569',
                    'bg_bottom': '#1e293b',
                    'hover_start': '#64748b',
                    'hover_mid': '#94a3b8',
                    'hover_end': '#475569',
                    'hover_bottom': '#334155',
                    'hover_border': '#475569',
                    'pressed_start': '#1e293b',
                    'pressed_mid': '#334155',
                    'pressed_end': '#0f172a',
                    'pressed_bottom': '#0f172a',
                    'pressed_border': '#334155',
                    'border': '#475569',
                    'text': '#ffffff'
                },
                'info': {
                    'bg_start': '#4fd1c7',
                    'bg_end': '#38b2ac',
                    'hover_start': '#81e6d9',
                    'hover_end': '#4fd1c7',
                    'pressed_start': '#38b2ac',
                    'pressed_end': '#319795',
                    'border': '#38b2ac',
                    'text': 'white'
                },
                'secondary': {
                    'bg_start': '#a0aec0',
                    'bg_end': '#718096',
                    'hover_start': '#cbd5e0',
                    'hover_end': '#a0aec0',
                    'pressed_start': '#718096',
                    'pressed_end': '#4a5568',
                    'border': '#718096',
                    'text': 'white'
                },
                'lime': {
                    'bg_start': '#365314',
                    'bg_mid': '#84cc16',
                    'bg_end': '#65a30d',
                    'bg_bottom': '#1a2e05',
                    'hover_start': '#84cc16',
                    'hover_mid': '#a3e635',
                    'hover_end': '#65a30d',
                    'hover_bottom': '#365314',
                    'hover_border': '#65a30d',
                    'pressed_start': '#1a2e05',
                    'pressed_mid': '#365314',
                    'pressed_end': '#14532d',
                    'pressed_bottom': '#14532d',
                    'pressed_border': '#365314',
                    'border': '#65a30d',
                    'text': '#ffffff'
                }
            }

            color_scheme = colors.get(button_type, colors['primary'])

            # تطبيق التصميم الراقي المتوافق مع Qt
            button.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.2 {color_scheme.get('bg_mid', color_scheme['bg_start'])},
                        stop:0.5 {color_scheme['bg_end']},
                        stop:0.8 {color_scheme.get('bg_bottom', color_scheme['bg_end'])},
                        stop:1 {color_scheme['bg_start']});
                    color: {color_scheme['text']};
                    border: 3px solid {color_scheme['border']};
                    border-radius: 12px;
                    padding: 10px 12px;
                    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                    font-size: 13px;
                    font-weight: bold;
                    min-height: 32px;
                    max-height: 36px;
                    margin: 0px;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.2 {color_scheme.get('hover_mid', color_scheme['hover_start'])},
                        stop:0.5 {color_scheme['hover_end']},
                        stop:0.8 {color_scheme.get('hover_bottom', color_scheme['hover_end'])},
                        stop:1 {color_scheme['hover_start']});
                    border: 3px solid {color_scheme.get('hover_border', color_scheme['hover_end'])};
                    border-radius: 14px;
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.2 {color_scheme.get('pressed_mid', color_scheme['pressed_start'])},
                        stop:0.5 {color_scheme['pressed_end']},
                        stop:0.8 {color_scheme.get('pressed_bottom', color_scheme['pressed_end'])},
                        stop:1 {color_scheme['pressed_start']});
                    border: 3px solid {color_scheme.get('pressed_border', color_scheme['pressed_end'])};
                    border-radius: 10px;
                }}
                QPushButton:disabled {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #f7fafc, stop:0.3 #e2e8f0, stop:0.7 #cbd5e0, stop:1 #a0aec0);
                    color: #718096;
                    border: 3px solid #cbd5e0;
                    border-radius: 12px;
                }}
                QPushButton::menu-indicator {{
                    {f"image: none; width: 0px;" if not has_menu else "width: 12px; height: 12px; margin-right: 4px;"}
                }}
            """)

            # تطبيق خط واضح وسهل القراءة
            font = QFont("Segoe UI", 13, QFont.Bold)  # خط واضح ومقروء
            font.setLetterSpacing(QFont.AbsoluteSpacing, 0.2)  # تباعد أحرف أقل
            font.setStyleHint(QFont.SansSerif)  # نوع الخط
            font.setHintingPreference(QFont.PreferFullHinting)  # تحسين وضوح الخط
            button.setFont(font)

        except Exception as e:
            print(f"❌ خطأ في تطبيق تصميم الزر المتطور: {str(e)}")

    def create_advanced_suppliers_table(self):
        """إنشاء جدول الموردين المتطور والمحسن مع مميزات متقدمة جداً"""
        print("🚀 بدء إنشاء جدول الموردين المتطور...")

        # إنشاء الجدول الأساسي مع تحسينات مرئية
        from PyQt5.QtWidgets import QTableWidget
        self.suppliers_table = QTableWidget()

        # إضافة العلامة المائية للجدول
        self.add_watermark_to_suppliers_table()

        # تطبيق نمط متطور جداً ومبتكر مع تأثيرات بصرية متقدمة
        self.suppliers_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.3);
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8fafc, stop:0.5 #f1f5f9, stop:1 #e2e8f0);
                border: 4px solid transparent;
                border-image: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:0.25 #764ba2, stop:0.5 #f093fb,
                    stop:0.75 #f5576c, stop:1 #4facfe) 1;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                font-size: 14px;
                selection-background-color: rgba(102, 126, 234, 0.25);
                alternate-background-color: rgba(241, 245, 249, 0.5);
                outline: none;
            }

            QTableWidget::item {
                padding: 15px 10px;
                border: 2px solid rgba(102, 126, 234, 0.15);
                border-left: 5px solid rgba(102, 126, 234, 0.4);
                border-right: 5px solid rgba(102, 126, 234, 0.4);
                text-align: center;
                min-height: 40px;
                max-height: 55px;
                font-weight: 600;
                font-size: 14px;
                border-radius: 12px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(248, 250, 252, 0.95));
                /* خلفية شفافة متدرجة تسمح بظهور ألوان الرصيد */
                box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.05);
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(102, 126, 234, 0.8),
                    stop:0.5 rgba(118, 75, 162, 0.8),
                    stop:1 rgba(240, 147, 251, 0.8)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                border-radius: 15px !important;
                font-weight: bold !important;
                box-shadow: 0px 4px 12px rgba(102, 126, 234, 0.4) !important;
                transform: scale(1.02) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.1),
                    stop:1 rgba(102, 126, 234, 0.2)) !important;
                border: 4px solid rgba(102, 126, 234, 0.6) !important;
                border-left: 6px solid #4facfe !important;
                border-right: 6px solid #4facfe !important;
                border-radius: 15px !important;
                box-shadow: 0px 6px 16px rgba(102, 126, 234, 0.3) !important;
                transform: translateY(-2px) scale(1.01) !important;
                transition: all 0.3s ease !important;
            }

            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9),
                    stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 5px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 8px solid #ffd700 !important;
                border-right: 8px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
                transform: scale(1.03) !important;
            }

            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:0.2 #764ba2, stop:0.4 #f093fb,
                    stop:0.6 #f5576c, stop:0.8 #4facfe, stop:1 #00f2fe);
                color: white;
                padding: 18px 12px;
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-bottom: 5px solid rgba(255, 255, 255, 0.5);
                font-weight: bold;
                font-size: 15px;
                text-align: center;
                border-radius: 15px 15px 0 0;
                margin: 0px;
                min-height: 60px;
                max-height: 60px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
            }

            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4facfe, stop:0.3 #00f2fe, stop:0.6 #667eea, stop:1 #764ba2);
                border: 5px solid rgba(255, 255, 255, 0.6);
                border-bottom: 6px solid #ffd700;
                transform: scale(1.02);
                box-shadow: 0px 4px 12px rgba(102, 126, 234, 0.4);
            }

            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2563EB, stop:0.5 #1D4ED8, stop:1 #1E40AF);
                border: 4px solid rgba(255, 255, 255, 0.8);
                border-bottom: 7px solid #ffd700;
                transform: scale(0.98);
            }

            QScrollBar:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(102, 126, 234, 0.1),
                    stop:1 rgba(102, 126, 234, 0.2));
                width: 18px;
                border-radius: 9px;
                margin: 3px;
                border: 2px solid rgba(102, 126, 234, 0.3);
            }

            QScrollBar::handle:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
                border-radius: 7px;
                min-height: 30px;
                border: 2px solid rgba(255, 255, 255, 0.4);
                margin: 2px;
            }

            QScrollBar::handle:vertical:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4facfe, stop:0.5 #00f2fe, stop:1 #667eea);
                border: 3px solid rgba(255, 255, 255, 0.6);
                transform: scale(1.1);
            }

            QScrollBar::handle:vertical:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2563EB, stop:0.5 #1D4ED8, stop:1 #1E40AF);
                border: 3px solid rgba(255, 255, 255, 0.8);
            }

            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }

            QScrollBar:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.1),
                    stop:1 rgba(102, 126, 234, 0.2));
                height: 18px;
                border-radius: 9px;
                margin: 3px;
                border: 2px solid rgba(102, 126, 234, 0.3);
            }

            QScrollBar::handle:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
                border-radius: 7px;
                min-width: 30px;
                border: 2px solid rgba(255, 255, 255, 0.4);
                margin: 2px;
            }

            QScrollBar::handle:horizontal:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4facfe, stop:0.5 #00f2fe, stop:1 #667eea);
                border: 3px solid rgba(255, 255, 255, 0.6);
            }

            QScrollBar::handle:horizontal:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2563EB, stop:0.5 #1D4ED8, stop:1 #1E40AF);
                border: 3px solid rgba(255, 255, 255, 0.8);
            }

            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
                width: 0px;
            }
        """)
        print("✅ تم إنشاء الجدول الأساسي")

        # تكوين الجدول المتطور
        self.setup_suppliers_table_structure()
        print("✅ تم إعداد هيكل الجدول")

        self.setup_suppliers_table_styling()
        print("✅ تم إعداد تنسيق الجدول")

        self.setup_suppliers_table_behavior()
        print("✅ تم إعداد سلوك الجدول")

        self.setup_suppliers_advanced_features()
        print("✅ تم إعداد المميزات المتقدمة")

        # إعادة تطبيق الأعراض للتأكد من تطبيقها
        self.apply_suppliers_column_widths()
        print(f"✅ تم تطبيق أعراض الأعمدة - الهاتف: {self.suppliers_table.columnWidth(2)}px, الرصيد: {self.suppliers_table.columnWidth(5)}px")

        # تطبيق الإطار الأسود البسيط
        self.force_suppliers_black_border_style()

        print("🎉 تم إنشاء جدول الموردين المتطور بنجاح!")

        # تطبيق التحسينات النهائية مرة واحدة
        print("🎨 تطبيق التحسينات النهائية...")
        self.apply_suppliers_final_enhancements()

        # فرض تطبيق الأعراض الصحيحة في النهاية
        self.apply_suppliers_correct_widths()

    def add_watermark_to_suppliers_table(self):
        """إضافة العلامة المائية لجدول الموردين"""
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QPainter, QFont, QColor

        # إنشاء فئة مخصصة للجدول مع العلامة المائية
        class WatermarkTableWidget(QTableWidget):
            def paintEvent(self, event):
                super().paintEvent(event)

                # رسم العلامة المائية
                painter = QPainter(self.viewport())
                painter.setRenderHint(QPainter.Antialiasing)

                # إعداد الخط والنص
                font = QFont("Arial", 48, QFont.Bold)
                painter.setFont(font)
                painter.setPen(QColor(200, 200, 200, 80))  # لون رمادي شفاف

                # النص والموضع
                text = "نظام إدارة الموردين"
                rect = self.viewport().rect()
                painter.drawText(rect, Qt.AlignCenter, text)
                painter.end()

        # استبدال الجدول العادي بالجدول مع العلامة المائية
        if hasattr(self, 'suppliers_table'):
            # نسخ الخصائص الحالية
            parent = self.suppliers_table.parent()
            geometry = self.suppliers_table.geometry()

            # إنشاء جدول جديد مع العلامة المائية
            new_table = WatermarkTableWidget()
            new_table.setParent(parent)
            new_table.setGeometry(geometry)

            # استبدال المرجع
            self.suppliers_table = new_table

        print("✅ تم إضافة العلامة المائية لجدول الموردين")

    def setup_suppliers_table_structure(self):
        """إعداد هيكل الجدول والأعمدة"""
        # تحديد عدد الأعمدة والعناوين المحسنة
        self.suppliers_table.setColumnCount(9)  # عدد مناسب من الأعمدة

        # عناوين محسنة ومختصرة مع أيقونات مختلفة - مطابقة للعملاء
        headers = [
            "🔢 ID",          # رقم مسلسل
            "👤 اسم المورد",   # الاسم كاملاً - نفس أيقونة العملاء
            "📱 الهاتف",      # رقم الهاتف
            "📧 الإيميل",      # البريد الإلكتروني
            "📍 العنوان",     # العنوان
            "💰 الرصيد",      # الرصيد الحالي
            "📝 ملاحظات",     # الملاحظات
            "🔄 الحالة",      # حالة المورد
            "📅 التاريخ"      # تاريخ الإنشاء
        ]

        self.suppliers_table.setHorizontalHeaderLabels(headers)

    def setup_suppliers_table_styling(self):
        """إعداد تنسيق وأسلوب الجدول"""
        header = self.suppliers_table.horizontalHeader()

        # تحسين خط العناوين - خط مميز ومتطور - مطابق للعملاء
        header_font = QFont("Segoe UI", 16, QFont.Bold)  # خط أكبر للعناوين
        header_font.setLetterSpacing(QFont.AbsoluteSpacing, 1.5)  # تباعد أحرف أوسع
        header_font.setStyleHint(QFont.SansSerif)  # نمط خط احترافي
        header.setFont(header_font)

        # تحسين ارتفاع رأس الجدول مع المزيد من الأناقة
        header.setFixedHeight(65)  # ارتفاع أكبر للعناوين المتطورة

        # إضافة تأثيرات بصرية للعناوين
        header.setDefaultAlignment(Qt.AlignCenter)  # توسيط العناوين

        # إعداد أعراض الأعمدة المحسنة مع عرض أكبر للتاريخ
        # تحديد الأعراض المحسنة
        fixed_widths = {
            0: 120,  # الرقم - أكبر
            1: 150,  # الاسم
            2: 220,  # الهاتف
            3: 180,  # الإيميل
            4: 150,  # العنوان
            5: 155,  # الرصيد - أصغر قليلاً
            6: 105,  # الملاحظات - أكبر قليلاً لاستيعاب النص
            7: 120,  # الحالة
            8: 154   # التاريخ - أكبر بكثير (استغلال المساحة المحررة)
        }

        # تطبيق الأعراض الثابتة
        for col, width in fixed_widths.items():
            self.suppliers_table.setColumnWidth(col, width)

        # إعداد سلوك تغيير الحجم لاستغلال العرض الكامل
        header.setSectionResizeMode(0, header.Fixed)      # الرقم ثابت
        header.setSectionResizeMode(1, header.Stretch)    # الاسم يتمدد
        header.setSectionResizeMode(2, header.Fixed)      # الهاتف ثابت
        header.setSectionResizeMode(3, header.Stretch)    # الإيميل يتمدد
        header.setSectionResizeMode(4, header.Stretch)    # العنوان يتمدد
        header.setSectionResizeMode(5, header.Fixed)      # الرصيد ثابت
        header.setSectionResizeMode(6, header.Stretch)    # ملاحظات يتمدد
        header.setSectionResizeMode(7, header.Fixed)      # الحالة ثابت
        header.setSectionResizeMode(8, header.Fixed)      # التاريخ ثابت - مهم!

        # تحسين ارتفاع الصفوف - أصغر
        vertical_header = self.suppliers_table.verticalHeader()
        vertical_header.setDefaultSectionSize(30)  # ارتفاع أصغر
        vertical_header.setMinimumSectionSize(25)
        vertical_header.setMaximumSectionSize(40)
        vertical_header.hide()  # إخفاء أرقام الصفوف لتوفير مساحة

    def setup_suppliers_table_behavior(self):
        """إعداد سلوك وتفاعل الجدول"""
        # تمكين الميزات التفاعلية الأصلية
        self.suppliers_table.setMouseTracking(True)
        self.suppliers_table.setAlternatingRowColors(False)  # تعطيل التلوين التلقائي لتجنب التضارب
        self.suppliers_table.setSortingEnabled(True)
        self.suppliers_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.suppliers_table.setSelectionMode(QTableWidget.SingleSelection)  # إرجاع التحديد الفردي

        # ربط الأحداث
        self.suppliers_table.doubleClicked.connect(self.edit_supplier)

        # ضمان استغلال العرض الكامل بدون تمديد العمود الأخير
        self.suppliers_table.horizontalHeader().setStretchLastSection(False)  # إيقاف تمديد العمود الأخير
        self.suppliers_table.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.suppliers_table.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # إزالة التداخل في CSS - لا حاجة لإضافة CSS إضافي



    def setup_suppliers_advanced_features(self):
        """إعداد المميزات المتقدمة للجدول"""
        # تطبيق المميزات المتقدمة
        pass

    def apply_suppliers_column_widths(self):
        """تطبيق أعراض الأعمدة مرة أخرى للتأكد - محسنة"""
        # الأعراض المحسنة مع عرض أكبر للتاريخ
        column_widths = {
            0: 120,  # الرقم - أكبر
            1: 150,  # الاسم
            2: 220,  # الهاتف
            3: 180,  # الإيميل
            4: 150,  # العنوان
            5: 155,  # الرصيد - أصغر قليلاً
            6: 105,  # الملاحظات - أكبر قليلاً لاستيعاب النص
            7: 120,  # الحالة
            8: 154   # التاريخ - أكبر بكثير (استغلال المساحة المحررة)
        }

        # تطبيق الأعراض مع التأكد من التطبيق
        for col, width in column_widths.items():
            self.suppliers_table.setColumnWidth(col, width)
            # التأكد من أن العمود لا يتمدد
            if col in [0, 2, 5, 7, 8]:  # الأعمدة الثابتة
                self.suppliers_table.horizontalHeader().setSectionResizeMode(col, self.suppliers_table.horizontalHeader().Fixed)



    def darken_color(self, color):
        """تعتيم اللون"""
        try:
            # إزالة # إذا كانت موجودة
            color = color.replace('#', '')

            # تحويل إلى RGB
            r = int(color[0:2], 16)
            g = int(color[2:4], 16)
            b = int(color[4:6], 16)

            # تعتيم بنسبة 20%
            r = max(0, int(r * 0.8))
            g = max(0, int(g * 0.8))
            b = max(0, int(b * 0.8))

            return f"#{r:02x}{g:02x}{b:02x}"
        except:
            return color

    def lighten_color(self, color):
        """تفتيح اللون"""
        try:
            # إزالة # إذا كانت موجودة
            color = color.replace('#', '')

            # تحويل إلى RGB
            r = int(color[0:2], 16)
            g = int(color[2:4], 16)
            b = int(color[4:6], 16)

            # تفتيح بنسبة 20%
            r = min(255, int(r * 1.2))
            g = min(255, int(g * 1.2))
            b = min(255, int(b * 1.2))

            return f"#{r:02x}{g:02x}{b:02x}"
        except:
            return color





    def filter_suppliers(self):
        """فلترة الموردين بالبحث"""
        search_text = self.search_edit.text().strip().lower()

        if not search_text:
            self.refresh_data()
            return

        try:
            suppliers = self.session.query(Supplier).filter(
                Supplier.name.like(f"%{search_text}%") |
                Supplier.phone.like(f"%{search_text}%") |
                Supplier.email.like(f"%{search_text}%") |
                Supplier.address.like(f"%{search_text}%") |
                Supplier.notes.like(f"%{search_text}%")
            ).all()

            self.populate_table(suppliers)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء البحث: {str(e)}")

    def filter_by_balance(self, balance_type):
        """فلترة الموردين حسب الرصيد"""
        try:
            if balance_type == 'positive':
                suppliers = self.session.query(Supplier).filter(Supplier.balance > 0).all()
            elif balance_type == 'negative':
                suppliers = self.session.query(Supplier).filter(Supplier.balance < 0).all()
            elif balance_type == 'zero':
                suppliers = self.session.query(Supplier).filter(Supplier.balance == 0).all()
            else:
                suppliers = self.session.query(Supplier).all()

            self.populate_table(suppliers)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء الفلترة: {str(e)}")

    def show_columns_dialog(self):
        """عرض حوار إدارة الأعمدة"""
        show_info_message("قريباً", "ميزة إدارة الأعمدة ستكون متاحة قريباً")

    def show_bulk_operations_dialog(self):
        """عرض حوار العمليات المجمعة"""
        show_info_message("قريباً", "ميزة العمليات المجمعة ستكون متاحة قريباً")

    def show_advanced_filters_dialog(self):
        """عرض حوار الفلاتر المتقدمة"""
        show_info_message("قريباً", "ميزة الفلاتر المتقدمة ستكون متاحة قريباً")

    def populate_table(self, suppliers):
        """ملء جدول الموردين المتطور بالبيانات المحسنة والمتقدمة"""
        print("📊 ملء الجدول بالبيانات المتطورة والمحسنة...")
        self.suppliers_table.setRowCount(0)

        for row, supplier in enumerate(suppliers):
            self.suppliers_table.insertRow(row)

            # خطوط متطورة وموحدة - حجم مناسب مع Bold
            unified_font = QFont("Segoe UI", 11, QFont.Bold)  # خط مناسب مع Bold
            unified_font.setLetterSpacing(QFont.AbsoluteSpacing, 0.5)
            unified_font.setStyleHint(QFont.SansSerif)
            unified_font.setWeight(QFont.Bold)

            unified_bold_font = QFont("Segoe UI", 11, QFont.Bold)  # نفس الحجم للعريض
            unified_bold_font.setLetterSpacing(QFont.AbsoluteSpacing, 0.8)
            unified_bold_font.setStyleHint(QFont.SansSerif)
            unified_bold_font.setWeight(QFont.ExtraBold)

            # 1. الرقم المسلسل مع تصميم متطور ومميز وأيقونة حسب الحالة
            # اختيار الأيقونة حسب الرصيد والحالة
            if supplier.balance > 10000:
                icon = "💎"  # VIP - مبلغ كبير جداً
            elif supplier.balance > 1000:
                icon = "⭐"  # ممتاز - مبلغ كبير
            elif supplier.balance > 0:
                icon = "🟢"  # نشط - مبلغ موجب
            elif supplier.balance == 0:
                icon = "✅"  # عادي - بدون رصيد
            elif supplier.balance > -1000:
                icon = "🟡"  # مدين بسيط
            elif supplier.balance > -5000:
                icon = "⚠️"  # تحذير - مدين متوسط
            else:
                icon = "🚨"  # خطر - مدين كبير

            id_display = f"{supplier.id} {icon}"
            id_item = QTableWidgetItem(id_display)
            id_item.setTextAlignment(Qt.AlignCenter)
            id_item.setFont(unified_bold_font)

            # لون أسود موحد وعريض لجميع أرقام ID
            id_item.setForeground(QColor("#000000"))  # أسود عريض موحد
            id_item.setBackground(QColor("#f8f9fa"))  # خلفية رمادية فاتحة موحدة

            # تحديد نوع المورد في التلميح فقط
            if supplier.id <= 10:
                id_item.setToolTip(f"👑 مورد مؤسس رقم: {supplier.id}")
            elif supplier.id <= 50:
                id_item.setToolTip(f"⭐ مورد مبكر رقم: {supplier.id}")
            else:
                id_item.setToolTip(f"🆔 رقم المورد: {supplier.id}")

            self.suppliers_table.setItem(row, 0, id_item)

            # 2. اسم المورد مع تصميم جذاب ومتطور
            # تحسين عرض الاسم
            display_name = supplier.name.title() if supplier.name else "غير محدد"
            if len(display_name) > 20:
                display_name = display_name[:17] + "..."

            name_item = QTableWidgetItem(display_name)
            name_item.setTextAlignment(Qt.AlignCenter)
            name_item.setFont(unified_bold_font)

            # لون أسود موحد وعريض لجميع الأسماء
            name_item.setForeground(QColor("#000000"))  # أسود عريض موحد
            name_item.setBackground(QColor("#f8f9fa"))  # خلفية رمادية فاتحة موحدة

            # تحديد نوع المورد في التلميح فقط
            if supplier.balance > 10000:
                name_item.setToolTip(f"👑 مورد VIP: {supplier.name}\n💰 رصيد عالي: {supplier.balance:,.0f} ر.س")
            elif supplier.balance > 0:
                name_item.setToolTip(f"✅ مورد نشط: {supplier.name}\n💰 رصيد موجب: {supplier.balance:,.0f} ر.س")
            elif supplier.balance < 0:
                name_item.setToolTip(f"⚠️ مورد مدين: {supplier.name}\n💸 مبلغ مستحق: {abs(supplier.balance):,.0f} ر.س")
            else:
                name_item.setToolTip(f"👤 مورد عادي: {supplier.name}\n💰 رصيد صفر")

            self.suppliers_table.setItem(row, 1, name_item)

            # 3. الهاتف مع تصميم احترافي ومتطور
            if supplier.phone:
                # تنسيق رقم الهاتف بشكل جميل
                phone_clean = supplier.phone.replace(" ", "").replace("-", "")
                if phone_clean.startswith("966"):
                    phone_display = f"+966 {phone_clean[3:6]} {phone_clean[6:9]} {phone_clean[9:]}"
                elif phone_clean.startswith("05"):
                    phone_display = f"0{phone_clean[1:3]} {phone_clean[3:6]} {phone_clean[6:]}"
                else:
                    phone_display = supplier.phone

                phone_item = QTableWidgetItem(phone_display)
                phone_item.setFont(unified_bold_font)

                # لون أسود موحد لجميع أرقام الهواتف
                phone_item.setForeground(QColor("#000000"))  # أسود موحد
                phone_item.setBackground(QColor("#f8f9fa"))  # خلفية رمادية فاتحة موحدة

                # تحديد نوع الرقم في التلميح فقط
                if phone_clean.startswith("966") or phone_clean.startswith("00966"):
                    phone_item.setToolTip(f"🌍 رقم دولي: {phone_display}\n📞 انقر نقرتين للاتصال\n📱 واتساب متاح")
                elif phone_clean.startswith("05"):
                    phone_item.setToolTip(f"📱 جوال سعودي: {phone_display}\n📞 انقر نقرتين للاتصال\n💬 واتساب متاح")
                else:
                    phone_item.setToolTip(f"☎️ هاتف: {phone_display}\n📞 انقر نقرتين للاتصال")
            else:
                phone_item = QTableWidgetItem("📵 غير متوفر")
                phone_item.setFont(unified_bold_font)  # خط عريض
                phone_item.setForeground(QColor("#DC2626"))  # أحمر للغير متوفر
                phone_item.setBackground(QColor("#FEE2E2"))  # أحمر فاتح جداً
                phone_item.setToolTip("📵 لا يوجد رقم هاتف مسجل\n💡 يمكن إضافة رقم من خلال التعديل")

            phone_item.setTextAlignment(Qt.AlignCenter)
            self.suppliers_table.setItem(row, 2, phone_item)

            # 4. البريد الإلكتروني مع تصميم متطور
            if supplier.email:
                email_text = supplier.email
                if len(email_text) > 25:
                    email_display = email_text[:22] + "..."
                else:
                    email_display = email_text

                email_item = QTableWidgetItem(email_display)
                email_item.setFont(unified_bold_font)  # خط عريض أسود

                # تحديد نوع البريد الإلكتروني - جميع الإيميلات بلون أسود موحد وعريض
                # تم توحيد لون جميع الإيميلات إلى الأسود العريض لتجنب الالتباس
                email_item.setForeground(QColor("#000000"))  # أسود عريض موحد لجميع الإيميلات
                email_item.setBackground(QColor("#f8f9fa"))  # خلفية رمادية فاتحة موحدة

                # تحديد نوع الإيميل في التلميح فقط
                if "@gmail.com" in supplier.email.lower():
                    email_item.setToolTip(f"📧 Gmail: {supplier.email}\n💡 انقر نقرتين لإرسال إيميل")
                elif "@outlook.com" in supplier.email.lower() or "@hotmail.com" in supplier.email.lower():
                    email_item.setToolTip(f"📧 Outlook: {supplier.email}\n💡 انقر نقرتين لإرسال إيميل")
                elif "@yahoo.com" in supplier.email.lower():
                    email_item.setToolTip(f"📧 Yahoo: {supplier.email}\n💡 انقر نقرتين لإرسال إيميل")
                else:
                    email_item.setToolTip(f"📧 إيميل: {supplier.email}\n💡 انقر نقرتين لإرسال إيميل")
            else:
                email_item = QTableWidgetItem("📧 غير متوفر")
                email_item.setFont(unified_bold_font)  # خط عريض
                email_item.setForeground(QColor("#DC2626"))  # أحمر للغير متوفر
                email_item.setBackground(QColor("#FEE2E2"))  # أحمر فاتح
                email_item.setToolTip("📧 لا يوجد بريد إلكتروني\n💡 يمكن إضافة إيميل من خلال التعديل")

            email_item.setTextAlignment(Qt.AlignCenter)
            self.suppliers_table.setItem(row, 3, email_item)

            # 5. العنوان
            address_text = supplier.address or "غير متوفر"
            if len(address_text) > 20:  # تقصير العناوين الطويلة
                address_display = address_text[:17] + "..."
            else:
                address_display = address_text

            address_item = QTableWidgetItem(address_display)
            address_item.setTextAlignment(Qt.AlignCenter)  # توسيط النص
            if supplier.address:
                address_item.setFont(unified_bold_font)  # خط عريض أسود
                address_item.setForeground(QColor("#000000"))  # أسود عريض
                address_item.setBackground(QColor("#f8f9fa"))  # خلفية رمادية فاتحة
                address_item.setToolTip(f"📍 العنوان: {supplier.address}\n💡 انقر نقرتين لفتح الخريطة")
            else:
                address_item.setFont(unified_bold_font)  # خط عريض
                address_item.setForeground(QColor("#DC2626"))  # أحمر للغير متوفر
                address_item.setBackground(QColor("#FEE2E2"))  # أحمر فاتح
                address_item.setToolTip("📍 لا يوجد عنوان مسجل\n💡 يمكن إضافة عنوان من خلال التعديل")
            self.suppliers_table.setItem(row, 4, address_item)

            # 6. الرصيد مع تصميم متطور وجذاب (بدون كسور وبدون ر.س)
            balance_text = f"{supplier.balance:,.0f}"
            balance_item = QTableWidgetItem(balance_text)
            balance_item.setTextAlignment(Qt.AlignCenter)
            # تطبيق خط موحد ثابت للرصيد
            balance_item.setFont(unified_bold_font)  # نفس الخط الموحد العريض

            # تلوين الرصيد حسب القيمة مع ألوان واضحة ومميزة
            if supplier.balance > 10000:
                # رصيد عالي جداً - أخضر ذهبي مميز
                balance_item.setForeground(QColor("#1a5f3f"))  # أخضر داكن
                balance_item.setBackground(QColor("#d4edda"))  # أخضر فاتح
                balance_item.setToolTip(f"💰 رصيد ممتاز: {balance_text}\n⭐ مورد VIP")
            elif supplier.balance > 0:
                # رصيد موجب - أخضر واضح
                balance_item.setForeground(QColor("#155724"))  # أخضر داكن
                balance_item.setBackground(QColor("#d1ecf1"))  # أخضر فاتح جداً
                balance_item.setToolTip(f"💰 رصيد موجب: {balance_text}\n✅ مورد نشط")
            elif supplier.balance < -5000:
                # رصيد سالب عالي - أحمر قوي
                balance_item.setForeground(QColor("#721c24"))  # أحمر داكن جداً
                balance_item.setBackground(QColor("#f8d7da"))  # أحمر فاتح
                balance_item.setToolTip(f"💰 رصيد مدين عالي: {balance_text}\n🚨 يحتاج متابعة")
            elif supplier.balance < 0:
                # رصيد سالب - أحمر واضح
                balance_item.setForeground(QColor("#856404"))  # برتقالي داكن
                balance_item.setBackground(QColor("#fff3cd"))  # أصفر فاتح
                balance_item.setToolTip(f"💰 رصيد مدين: {balance_text}\n⚠️ يحتاج متابعة")
            else:
                # رصيد صفر - رمادي
                balance_item.setForeground(QColor("#495057"))  # رمادي داكن
                balance_item.setBackground(QColor("#e9ecef"))  # رمادي فاتح
                balance_item.setToolTip(f"💰 رصيد صفر: {balance_text}\n➖ لا يوجد رصيد")

            self.suppliers_table.setItem(row, 5, balance_item)

            # 7. الملاحظات مع عرض أفضل
            notes_text = supplier.notes or "غير متوفر"
            if len(notes_text) > 25:  # زيادة الحد الأقصى للنص
                notes_display = notes_text[:22] + "..."
            else:
                notes_display = notes_text

            notes_item = QTableWidgetItem(notes_display)
            notes_item.setTextAlignment(Qt.AlignCenter)  # توسيط النص
            if supplier.notes:
                notes_item.setFont(unified_bold_font)  # خط عريض أسود
                notes_item.setForeground(QColor("#000000"))  # أسود عريض
                notes_item.setBackground(QColor("#f8f9fa"))  # خلفية رمادية فاتحة
                notes_item.setToolTip(supplier.notes)  # النص الكامل في التلميح
            else:
                notes_item.setFont(unified_bold_font)  # خط عريض
                notes_item.setForeground(QColor("#DC2626"))  # أحمر للغير متوفر
                notes_item.setBackground(QColor("#FEE2E2"))  # أحمر فاتح
                notes_item.setToolTip("📝 لا توجد ملاحظات\n💡 يمكن إضافة ملاحظات من خلال التعديل")
            self.suppliers_table.setItem(row, 6, notes_item)

            # 8. الحالة مع تصميم احترافي محسن
            # تحديد الحالة بناءً على الرصيد مع إضافة حالة "عادي"
            if supplier.balance > 0:
                status_text = "🟢 نشط"
                status_color = QColor("#38a169")  # أخضر للنشط
                status_bg = QColor("#c6f6d5")     # خلفية خضراء فاتحة
            elif supplier.balance == 0:
                status_text = "⚫ عادى"  # النص مع علامة سوداء
                status_color = QColor("#000000")  # أسود للعادى
                status_bg = QColor("#f8f9fa")     # خلفية رمادية فاتحة
            else:
                status_text = "🔴 مدين"
                status_color = QColor("#e53e3e")  # أحمر للمدين
                status_bg = QColor("#fed7d7")     # خلفية حمراء فاتحة

            # إنشاء عنصر الحالة مع تنسيق موحد
            status_item = QTableWidgetItem(status_text)
            status_item.setTextAlignment(Qt.AlignCenter)

            # تطبيق خط موحد للحالة
            status_item.setFont(unified_bold_font)  # نفس الخط الموحد العريض

            # تطبيق الألوان
            status_item.setForeground(status_color)
            status_item.setBackground(status_bg)

            # إضافة تلميح للحالة
            if supplier.balance > 0:
                status_item.setToolTip(f"🟢 مورد نشط\n💰 الرصيد: {supplier.balance:,.0f}")
            elif supplier.balance == 0:
                status_item.setToolTip(f"⚫ مورد عادى\n💰 الرصيد: صفر")
            else:
                status_item.setToolTip(f"🔴 مورد مدين\n💰 المبلغ المستحق: {abs(supplier.balance):,.0f}")

            self.suppliers_table.setItem(row, 7, status_item)

            # 9. التاريخ
            if hasattr(supplier, 'created_at') and supplier.created_at:
                date_text = supplier.created_at.strftime('%Y-%m-%d')
                date_item = QTableWidgetItem(date_text)
                date_item.setForeground(QColor("#000000"))  # أسود عريض
                date_item.setBackground(QColor("#f8f9fa"))  # خلفية رمادية فاتحة
                date_item.setToolTip(f"📅 تاريخ الإضافة: {date_text}")
            else:
                date_text = "غير متوفر"
                date_item = QTableWidgetItem(date_text)
                date_item.setForeground(QColor("#DC2626"))  # أحمر للغير متوفر
                date_item.setBackground(QColor("#FEE2E2"))  # أحمر فاتح
                date_item.setToolTip("📅 لا يوجد تاريخ إضافة مسجل")

            date_item.setTextAlignment(Qt.AlignCenter)
            date_item.setFont(unified_bold_font)  # خط عريض أسود
            self.suppliers_table.setItem(row, 8, date_item)

        # تطبيق الأعراض الجديدة مباشرة مع فرض التطبيق
        print("📏 فرض تطبيق الأعراض الجديدة...")

        # أولاً: تعيين الأعمدة الثابتة
        header = self.suppliers_table.horizontalHeader()
        header.setSectionResizeMode(8, header.Fixed)  # التاريخ ثابت

        # ثانياً: تطبيق الأعراض المحسنة مع عرض أكبر للتاريخ
        self.suppliers_table.setColumnWidth(0, 120)  # الرقم - أكبر
        self.suppliers_table.setColumnWidth(1, 150)  # الاسم
        self.suppliers_table.setColumnWidth(2, 220)  # الهاتف
        self.suppliers_table.setColumnWidth(3, 180)  # الإيميل
        self.suppliers_table.setColumnWidth(4, 150)  # العنوان
        self.suppliers_table.setColumnWidth(5, 155)  # الرصيد - أصغر قليلاً
        self.suppliers_table.setColumnWidth(6, 105)  # الملاحظات - أكبر قليلاً لاستيعاب النص
        self.suppliers_table.setColumnWidth(7, 120)  # الحالة
        self.suppliers_table.setColumnWidth(8, 154)  # التاريخ - أكبر بكثير (استغلال المساحة المحررة)

        print("✅ تم فرض تطبيق الأعراض الجديدة")

        print("✅ تم ملء الجدول بالبيانات المحسنة والمطورة")

        # تطبيق التحسينات النهائية بعد ملء البيانات
        self.apply_suppliers_final_enhancements()

    def apply_suppliers_final_enhancements(self):
        """تطبيق التحسينات النهائية على جدول الموردين"""
        try:
            print("🎨 تطبيق التحسينات النهائية على جدول الموردين...")

            # تطبيق ألوان الرصيد مرة أخيرة للتأكد
            self.apply_suppliers_balance_colors()

            # تطبيق الأعراض الصحيحة
            self.apply_suppliers_correct_widths()

            # تحديث ألوان العناوين لتطابق العملاء
            self.update_suppliers_header_colors()

            # تطبيق الإطار الأسود
            self.force_suppliers_black_border_style()

            print("✅ تم تطبيق التحسينات النهائية على جدول الموردين بنجاح!")

        except Exception as e:
            print(f"❌ خطأ في تطبيق التحسينات النهائية: {str(e)}")

    def apply_suppliers_balance_colors(self):
        """تطبيق ألوان الرصيد على جدول الموردين"""
        try:
            print("🎨 تطبيق ألوان الرصيد على جدول الموردين...")

            for row in range(self.suppliers_table.rowCount()):
                balance_item = self.suppliers_table.item(row, 5)  # عمود الرصيد
                if balance_item:
                    balance_text = balance_item.text().replace(',', '')
                    try:
                        balance = float(balance_text)

                        # تطبيق الألوان حسب قيمة الرصيد
                        if balance > 10000:
                            # رصيد عالي جداً - أخضر ذهبي
                            balance_item.setForeground(QColor("#1a5f3f"))
                            balance_item.setBackground(QColor("#d4edda"))
                            print(f"🟢 أخضر ذهبي للصف {row} (رصيد: {balance})")
                        elif balance > 0:
                            # رصيد موجب - أخضر
                            balance_item.setForeground(QColor("#155724"))
                            balance_item.setBackground(QColor("#d1ecf1"))
                            print(f"🟢 أخضر للصف {row} (رصيد: {balance})")
                        elif balance < -5000:
                            # رصيد سالب عالي - أحمر قوي
                            balance_item.setForeground(QColor("#721c24"))
                            balance_item.setBackground(QColor("#f8d7da"))
                            print(f"🔴 أحمر قوي للصف {row} (رصيد: {balance})")
                        elif balance < 0:
                            # رصيد سالب - برتقالي
                            balance_item.setForeground(QColor("#856404"))
                            balance_item.setBackground(QColor("#fff3cd"))
                            print(f"🟠 برتقالي للصف {row} (رصيد: {balance})")
                        else:
                            # رصيد صفر - رمادي
                            balance_item.setForeground(QColor("#495057"))
                            balance_item.setBackground(QColor("#e9ecef"))
                            print(f"⚪ رمادي للصف {row} (رصيد: {balance})")

                    except ValueError:
                        pass

            print("✅ تم تطبيق ألوان الرصيد على جدول الموردين بنجاح!")

        except Exception as e:
            print(f"❌ خطأ في تطبيق ألوان الرصيد: {str(e)}")

    def apply_suppliers_correct_widths(self):
        """تطبيق الأعراض الصحيحة لجدول الموردين مطابقة للعملاء"""
        try:
            print("🔧 تطبيق الأعراض الصحيحة لجدول الموردين...")

            # تطبيق الأعراض المحسنة مع عرض أكبر للتاريخ
            self.suppliers_table.setColumnWidth(0, 120)  # الرقم - أكبر
            self.suppliers_table.setColumnWidth(1, 150)  # الاسم
            self.suppliers_table.setColumnWidth(2, 220)  # الهاتف
            self.suppliers_table.setColumnWidth(3, 180)  # الإيميل
            self.suppliers_table.setColumnWidth(4, 150)  # العنوان
            self.suppliers_table.setColumnWidth(5, 155)  # الرصيد - أصغر قليلاً
            self.suppliers_table.setColumnWidth(6, 105)  # الملاحظات - أكبر قليلاً لاستيعاب النص
            self.suppliers_table.setColumnWidth(7, 120)  # الحالة
            self.suppliers_table.setColumnWidth(8, 154)  # التاريخ - أكبر بكثير (استغلال المساحة المحررة)

            # فرض التحديث
            self.suppliers_table.update()
            self.suppliers_table.repaint()

            # تطبيق ارتفاع الصفوف المحسن - أصغر
            for row in range(self.suppliers_table.rowCount()):
                self.suppliers_table.setRowHeight(row, 40)  # ارتفاع أصغر للصفوف

            print("✅ تم تطبيق الأعراض الصحيحة لجدول الموردين بنجاح!")
            print(f"📏 عرض الرقم: {self.suppliers_table.columnWidth(0)}px")
            print(f"📏 عرض الاسم: {self.suppliers_table.columnWidth(1)}px")
            print(f"📏 عرض الهاتف: {self.suppliers_table.columnWidth(2)}px")
            print(f"📏 عرض الإيميل: {self.suppliers_table.columnWidth(3)}px")
            print(f"📏 عرض العنوان: {self.suppliers_table.columnWidth(4)}px")
            print(f"📏 عرض الرصيد: {self.suppliers_table.columnWidth(5)}px")
            print(f"📏 عرض الملاحظات: {self.suppliers_table.columnWidth(6)}px")
            print(f"📏 عرض الحالة: {self.suppliers_table.columnWidth(7)}px")
            print(f"📏 عرض التاريخ: {self.suppliers_table.columnWidth(8)}px")
            print(f"📏 ارتفاع الصفوف: 40px")

        except Exception as e:
            print(f"❌ خطأ في تطبيق الأعراض: {str(e)}")

    def force_suppliers_black_border_style(self):
        """تطبيق إطار أسود بسيط فقط مع الحفاظ على التصميم الأصلي للموردين"""
        try:
            # الحصول على التنسيق الحالي وتعديل الإطار فقط
            current_style = self.suppliers_table.styleSheet()

            # إضافة الإطار الأسود فقط
            black_border_addition = """
                QTableWidget {
                    border: 3px solid #000000 !important;
                }
            """

            # دمج التنسيق الحالي مع الإطار الأسود
            combined_style = current_style + black_border_addition
            self.suppliers_table.setStyleSheet(combined_style)

            print("✅ تم تطبيق الإطار الأسود البسيط للموردين")

        except Exception as e:
            print(f"❌ خطأ في تطبيق الإطار الأسود للموردين: {str(e)}")

    def update_suppliers_header_colors(self):
        """تحديث ألوان عناوين جدول الموردين لتطابق العملاء"""
        try:
            print("🎨 تحديث ألوان عناوين جدول الموردين...")

            header = self.suppliers_table.horizontalHeader()

            # تطبيق نفس الخط والارتفاع من جدول العملاء
            header_font = QFont("Segoe UI", 15, QFont.Bold)  # خط متوسط ومميز
            header_font.setLetterSpacing(QFont.AbsoluteSpacing, 1.0)  # تباعد الأحرف
            header.setFont(header_font)

            # تطبيق نفس الارتفاع من جدول العملاء
            header.setFixedHeight(55)  # نفس ارتفاع العملاء

            # تطبيق نفس ألوان العناوين من جدول العملاء
            header.setStyleSheet("""
                QHeaderView::section {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
                    color: white;
                    border: 3px solid #5a67d8;
                    border-bottom: 5px solid #4c51bf;
                    padding: 15px 8px;
                    font-weight: bold;
                    font-size: 14px;
                    text-align: center;
                    min-height: 35px;
                    max-height: 55px;
                    letter-spacing: 1.0px;
                }
                QHeaderView::section:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #5a67d8, stop:0.5 #667eea, stop:1 #e0c3fc);
                    border: 3px solid #4c51bf;
                    border-bottom: 5px solid #3c366b;
                }
                QHeaderView::section:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #4c51bf, stop:0.5 #553c9a, stop:1 #c084fc);
                    border: 3px solid #3c366b;
                    border-bottom: 5px solid #2d3748;
                }
            """)

            print("✅ تم تحديث ألوان عناوين جدول الموردين بنجاح!")

        except Exception as e:
            print(f"❌ خطأ في تحديث ألوان العناوين: {str(e)}")

    def refresh_data(self):
        """تحديث البيانات"""
        try:
            suppliers = self.session.query(Supplier).all()
            self.populate_table(suppliers)
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تحميل البيانات: {str(e)}")

    def modern_refresh_data(self):
        """تحديث البيانات بطريقة حديثة"""
        self.refresh_data()
        show_info_message("تم", "تم تحديث البيانات بنجاح")

    # دوال أساسية للموردين
    def add_supplier(self):
        """إضافة مورد جديد"""
        from ui.dialogs import SupplierDialog
        dialog = SupplierDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            self.refresh_data()

    def edit_supplier(self):
        """تعديل مورد محدد"""
        selected_row = self.suppliers_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار مورد من القائمة")
            return

        # استخراج الرقم من النص الذي يحتوي على أيقونات
        id_text = self.suppliers_table.item(selected_row, 0).text()
        supplier_id = int(id_text.split()[0])  # أخذ الرقم فقط قبل المسافة
        supplier = self.session.query(Supplier).get(supplier_id)

        if not supplier:
            show_error_message("خطأ", "لم يتم العثور على المورد")
            return

        from ui.dialogs import SupplierDialog
        dialog = SupplierDialog(self, supplier)
        if dialog.exec_() == QDialog.Accepted:
            self.refresh_data()

    def delete_supplier(self):
        """حذف مورد محدد"""
        selected_row = self.suppliers_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار مورد من القائمة")
            return

        # استخراج الرقم من النص الذي يحتوي على أيقونات
        id_text = self.suppliers_table.item(selected_row, 0).text()
        supplier_id = int(id_text.split()[0])  # أخذ الرقم فقط قبل المسافة
        supplier = self.session.query(Supplier).get(supplier_id)

        if not supplier:
            show_error_message("خطأ", "لم يتم العثور على المورد")
            return

        if show_confirmation_message("تأكيد الحذف", f"هل أنت متأكد من حذف المورد '{supplier.name}'؟"):
            try:
                self.session.delete(supplier)
                self.session.commit()
                show_info_message("تم", "تم حذف المورد بنجاح")
                self.refresh_data()
            except Exception as e:
                self.session.rollback()
                show_error_message("خطأ", f"حدث خطأ أثناء حذف المورد: {str(e)}")

    def adjust_balance(self):
        """تعديل رصيد المورد"""
        show_info_message("قريباً", "ميزة تعديل الرصيد ستكون متاحة قريباً")

    def manage_attachments(self):
        """إدارة المرفقات"""
        show_info_message("قريباً", "ميزة إدارة المرفقات ستكون متاحة قريباً")

    def show_supplier_details(self):
        """عرض تفاصيل المورد"""
        show_info_message("قريباً", "ميزة عرض التفاصيل ستكون متاحة قريباً")

    def show_financial_details(self):
        """عرض التفاصيل المالية"""
        show_info_message("قريباً", "ميزة التفاصيل المالية ستكون متاحة قريباً")

    def show_contact_details(self):
        """عرض تفاصيل الاتصال"""
        show_info_message("قريباً", "ميزة تفاصيل الاتصال ستكون متاحة قريباً")

    def show_transaction_history(self):
        """عرض سجل المعاملات"""
        show_info_message("قريباً", "ميزة سجل المعاملات ستكون متاحة قريباً")

    def export_data(self):
        """تصدير البيانات"""
        show_info_message("قريباً", "ميزة التصدير ستكون متاحة قريباً")

    def export_to_pdf(self):
        """تصدير إلى PDF"""
        show_info_message("قريباً", "ميزة التصدير إلى PDF ستكون متاحة قريباً")

    def export_to_csv(self):
        """تصدير إلى CSV"""
        show_info_message("قريباً", "ميزة التصدير إلى CSV ستكون متاحة قريباً")

    def export_to_json(self):
        """تصدير إلى JSON"""
        show_info_message("قريباً", "ميزة التصدير إلى JSON ستكون متاحة قريباً")

    def generate_suppliers_report(self):
        """إنشاء تقرير الموردين"""
        show_info_message("قريباً", "ميزة التقارير ستكون متاحة قريباً")

    def backup_suppliers_data(self):
        """نسخ احتياطي لبيانات الموردين"""
        show_info_message("قريباً", "ميزة النسخ الاحتياطي ستكون متاحة قريباً")

    def restore_suppliers_data(self):
        """استعادة بيانات الموردين"""
        show_info_message("قريباً", "ميزة الاستعادة ستكون متاحة قريباً")

    def quick_add_supplier(self):
        """إضافة سريعة للمورد"""
        show_info_message("قريباً", "ميزة الإضافة السريعة ستكون متاحة قريباً")

    def import_suppliers(self):
        """استيراد الموردين من ملف"""
        show_info_message("قريباً", "ميزة الاستيراد ستكون متاحة قريباً")

    def make_direct_call(self):
        """اتصال مباشر"""
        show_info_message("قريباً", "ميزة الاتصال المباشر ستكون متاحة قريباً")

    def make_whatsapp_call(self):
        """اتصال واتساب"""
        show_info_message("قريباً", "ميزة اتصال واتساب ستكون متاحة قريباً")

    def copy_phone_number(self):
        """نسخ رقم الهاتف"""
        show_info_message("قريباً", "ميزة نسخ الرقم ستكون متاحة قريباً")

    def save_to_contacts(self):
        """حفظ في جهات الاتصال"""
        show_info_message("قريباً", "ميزة حفظ جهات الاتصال ستكون متاحة قريباً")

    def show_call_history(self):
        """عرض سجل المكالمات"""
        show_info_message("قريباً", "ميزة سجل المكالمات ستكون متاحة قريباً")

    # دوال الإحصائيات والتقارير المتقدمة
    def show_statistics(self):
        """عرض الإحصائيات الأساسية"""
        show_info_message("قريباً", "ميزة الإحصائيات الأساسية ستكون متاحة قريباً")

    def show_detailed_statistics(self):
        """عرض الإحصائيات المفصلة"""
        show_info_message("قريباً", "ميزة الإحصائيات المفصلة ستكون متاحة قريباً")

    def show_balance_analysis(self):
        """عرض تحليل الأرصدة"""
        show_info_message("قريباً", "ميزة تحليل الأرصدة ستكون متاحة قريباً")

    def show_monthly_report(self):
        """عرض التقرير الشهري"""
        show_info_message("قريباً", "ميزة التقرير الشهري ستكون متاحة قريباً")
